<?php

/**
 * 公共方法
 *
 * @package    function
 * @copyright  Copyright (c) 2007-2018 ShopNC Inc. (http://www.shopnc.net)
 * @license    http://www.shopnc.net/
 * @link       http://www.shopnc.net/
 * <AUTHOR> Team
 * @since      File available since Release v1.1
 */

use Curl\Curl;
use Shopnc\Log;
use Shopnc\Tpl;
use Upet\Integrates\Redis\RedisManager as Redis;
use Upet\Integrates\Storage\Storage;
use Upet\Models\Order;
use Upet\Models\OrderCommon;
use Upet\Models\VrOrder;

defined('InShopNC') or exit('Access Invalid!');

require_once "url.php";

/**
 * 产生验证码
 *
 * @param string $nchash 哈希数
 * @return string
 */
function makeSeccode($nchash)
{
    $seccode = random(6, 1);
    $seccodeunits = '';
    $s = sprintf('%04s', base_convert($seccode, 10, 23));
    $seccodeunits = 'ABCEFGHJKMPRTVXY2346789';
    if ($seccodeunits) {
        $seccode = '';
        for ($i = 0; $i < 4; $i++) {
            $unit = ord($s[$i]);
            $seccode .= ($unit >= 0x30 && $unit <= 0x39) ? $seccodeunits[$unit - 0x30] : $seccodeunits[$unit - 0x57];
        }
    }
    setNcCookie('seccode', encrypt(strtoupper($seccode) . "\t" . time(), MD5_KEY), 3600);
    return $seccode;
}

/**
 * 验证验证码
 *
 * @param string $nchash 哈希数
 * @param string $value 待验证值
 * @return boolean
 */
function checkSeccode($nchash, $value)
{
    list($checkvalue, $checktime) = explode("\t", decrypt(cookie('seccode'), MD5_KEY));
    $return = $checkvalue == strtoupper($value);
    if (!$return) setNcCookie('seccode', '', -3600);
    return $return;
}

/**
 * 设置cookie
 *
 * @param string $name cookie 的名称
 * @param string $value cookie 的值
 * @param int $expire cookie 有效周期
 * @param string $path cookie 的服务器路径 默认为 /
 * @param string $domain cookie 的域名
 * @param string $secure 是否通过安全的 HTTPS 连接来传输 cookie,默认为false
 */
function setNcCookie($name, $value, $expire = '3600', $path = '', $domain = '', $secure = false)
{
    if (empty($path)) $path = '/';
    if (empty($domain)) $domain = SUBDOMAIN_SUFFIX ? SUBDOMAIN_SUFFIX : '';
    $name = defined('COOKIE_PRE') ? COOKIE_PRE . $name : strtoupper(substr(md5(MD5_KEY), 0, 4)) . '_' . $name;
    $expire = intval($expire) ? intval($expire) : (intval(SESSION_EXPIRE) ? intval(SESSION_EXPIRE) : 3600);
    $result = setcookie($name, $value, time() + $expire, $path, $domain, $secure);
    $_COOKIE[$name] = $value;
}

/**
 * 取得COOKIE的值
 *
 * @param string $name
 * @return unknown
 */
function cookie($name = '')
{
    $name = defined('COOKIE_PRE') ? COOKIE_PRE . $name : strtoupper(substr(md5(MD5_KEY), 0, 4)) . '_' . $name;
    return $_COOKIE[$name];
}

/**
 * 当访问的act或op不存在时调用此函数并退出脚本
 *
 * @param string $act
 * @param string $op
 * @return void
 */
function requestNotFound($act = null, $op = null)
{
    showMessage('您访问的页面不存在！', SHOP_SITE_URL, 'exception', 'error', 1, 3000);
    exit;
}

/**
 * 输出信息
 *
 * @param string $msg 输出信息
 * @param string/array $url 跳转地址 当$url为数组时，结构为 array('msg'=>'跳转连接文字','url'=>'跳转连接');
 * @param string $show_type 输出格式 默认为html
 * @param string $msg_type 信息类型 succ 为成功，error为失败/错误
 * @param string $is_show  是否显示跳转链接，默认是为1，显示
 * @param int $time 跳转时间，默认为2秒
 * @return string 字符串类型的返回结果
 */
function showMessage($msg, $url = '', $show_type = 'html', $msg_type = 'succ', $is_show = 1, $time = 2000)
{
    if (!class_exists('Language')) import('libraries.language');
    Language::read('core_lang_index');
    $lang   = Language::getLangContent();
    /**
     * 如果默认为空，则跳转至上一步链接
     */
    $url = ($url != '' ? $url : getReferer());

    $msg_type = in_array($msg_type, array('succ', 'error')) ? $msg_type : 'error';

    /**
     * 输出类型
     */
    switch ($show_type) {
        case 'json':
            $return = '{';
            $return .= '"msg":"' . $msg . '",';
            $return .= '"url":"' . $url . '"';
            $return .= '}';
            echo $return;
            break;
        case 'exception':
            echo '<!DOCTYPE html>';
            echo '<html>';
            echo '<head>';
            echo '<meta http-equiv="Content-Type" content="text/html; charset=' . CHARSET . '" />';
            echo '<title></title>';
            echo '<style type="text/css">';
            echo 'body { font-family: "Verdana";padding: 0; margin: 0;}';
            echo 'h2 { font-size: 12px; line-height: 30px; border-bottom: 1px dashed #CCC; padding-bottom: 8px;width:800px; margin: 20px 0 0 150px;}';
            echo 'dl { float: left; display: inline; clear: both; padding: 0; margin: 10px 20px 20px 150px;}';
            echo 'dt { font-size: 14px; font-weight: bold; line-height: 40px; color: #333; padding: 0; margin: 0; border-width: 0px;}';
            echo 'dd { font-size: 12px; line-height: 40px; color: #333; padding: 0px; margin:0;}';
            echo '</style>';
            echo '</head>';
            echo '<body>';
            echo '<h2>' . $lang['error_info'] . '</h2>';
            echo '<dl>';
            echo '<dd>' . $msg . '</dd>';
            echo '<dt><p /></dt>';
            echo '<dd>' . $lang['error_notice_operate'] . '</dd>';
            echo '<dd><p /><p /><p /><p /></dd>';
            echo '</dl>';
            echo '</body>';
            echo '</html>';
            exit;
            break;
        case 'javascript':
            echo "<script>";
            echo "alert('" . $msg . "');";
            echo "location.href='" . $url . "'";
            echo "</script>";
            exit;
            break;
        case 'tenpay':
            echo "<html><head>";
            echo "<meta name=\"TENCENT_ONLINE_PAYMENT\" content=\"China TENCENT\">";
            echo "<script language=\"javascript\">";
            echo "window.location.href='" . $url . "';";
            echo "</script>";
            echo "</head><body></body></html>";
            exit;
            break;
        default:
            /**
             * 不显示右侧工具条
             */
            Tpl::output('hidden_nctoolbar', 1);
            if (is_array($url)) {
                foreach ($url as $k => $v) {
                    $url[$k]['url'] = $v['url'] ? $v['url'] : getReferer();
                }
            }
            /**
             * 读取信息布局的语言包
             */
            Language::read("msg");
            /**
             * html输出形式
             * 指定为指定项目目录下的error模板文件
             */
            Tpl::setDir('');
            Tpl::output('html_title', Language::get('nc_html_title'));
            Tpl::output('msg', $msg);
            Tpl::output('url', $url);
            Tpl::output('msg_type', $msg_type);
            Tpl::output('is_show', $is_show);
            Tpl::showpage('msg', 'msg_layout', $time);
    }
    exit;
}

/**
 * 消息提示，主要适用于普通页面AJAX提交的情况
 *
 * @param string $message 消息内容
 * @param string $url 提示完后的URL去向
 * @param stting $alert_type 提示类型 error/succ/notice 分别为错误/成功/警示
 * @param string $extrajs 扩展JS
 * @param int $time 停留时间
 */
function showDialog($message = '', $url = '', $alert_type = 'error', $extrajs = '', $time = 2)
{
    if (empty($_GET['inajax'])) {
        if ($url == 'reload') $url = '';
        showMessage($message . $extrajs, $url, 'html', $alert_type, 1, $time * 1000);
    }
    $message = str_replace("'", "\\'", strip_tags($message));

    $paramjs = null;
    if ($url == 'reload') {
        $paramjs = 'window.location.reload()';
    } elseif ($url != '') {
        $paramjs = 'window.location.href =\'' . $url . '\'';
    }
    if ($paramjs) {
        $paramjs = 'function (){' . $paramjs . '}';
    } else {
        $paramjs = 'null';
    }
    $modes = array('error' => 'alert', 'succ' => 'succ', 'notice' => 'notice', 'js' => 'js');
    $cover = $alert_type == 'error' ? 1 : 0;
    $extra = '';
    $extra .= 'showDialog(\'' . $message . '\', \'' . $modes[$alert_type] . '\', null, ' . ($paramjs ? $paramjs : 'null') . ', ' . $cover . ', null, null, null, null, ' . (is_numeric($time) ? $time : 'null') . ', null);';
    $extra = $extra ? '<script type="text/javascript" reload="1">' . $extra . '</script>' : '';
    if ($extrajs != '' && substr(trim($extrajs), 0, 7) != '<script') {
        $extrajs = '<script type="text/javascript" reload="1">' . $extrajs . '</script>';
    }
    $extra .= $extrajs;
    ob_end_clean();
    @header("Expires: -1");
    @header("Cache-Control: no-store, private, post-check=0, pre-check=0, max-age=0", FALSE);
    @header("Pragma: no-cache");
    @header("Content-type: text/xml; charset=" . CHARSET);

    $string =  '<?xml version="1.0" encoding="' . CHARSET . '"?>' . "\r\n";
    $string .= '<root><![CDATA[' . $message . $extra . ']]></root>';
    echo $string;
    exit;
}

/**
 * 不显示信息直接跳转
 *
 * @param string $url
 */
function redirect($url = '')
{
    if (empty($url)) {
        if (!empty($_REQUEST['ref_url'])) {
            $url = $_REQUEST['ref_url'];
        } else {
            $url = getReferer();
        }
    }
    header('Location: ' . $url);
    exit();
}

/**
 * 取上一步来源地址
 *
 * @param
 * @return string 字符串类型的返回结果
 */
function getReferer()
{
    return str_replace(array('\'', '"', '<', '>'), '', $_SERVER['HTTP_REFERER']);
}

/**
 * 取验证码hash值
 *
 * @param
 * @return string 字符串类型的返回结果
 */
function getNchash($act = '', $op = '')
{
    $act = $act ? $act : $_GET['act'];
    $op = $op ? $op : $_GET['op'];
    if (C('captcha_status_login')) {
        return substr(md5(SHOP_SITE_URL . $act . $op), 0, 8);
    } else {
        return '';
    }
}

/**
 * 加密函数
 *
 * @param string $txt 需要加密的字符串
 * @param string $key 密钥
 * @return string 返回加密结果
 */
function encrypt($txt, $key = '')
{
    if (empty($txt)) return $txt;
    if (empty($key)) $key = md5(MD5_KEY);
    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.";
    $ikey = "-x6g6ZWm2G9g_vr0Bo.pOq3kRIxsZ6rm";
    $nh1 = rand(0, 64);
    $nh2 = rand(0, 64);
    $nh3 = rand(0, 64);
    $ch1 = $chars[$nh1];
    $ch2 = $chars[$nh2];
    $ch3 = $chars[$nh3];
    $nhnum = $nh1 + $nh2 + $nh3;
    $knum = 0;
    $i = 0;
    while (isset($key[$i])) $knum += ord($key[$i++]);
    $mdKey = substr(md5(md5(md5($key . $ch1) . $ch2 . $ikey) . $ch3), $nhnum % 8, $knum % 8 + 16);
    $txt = base64_encode(time() . '_' . $txt);
    $txt = str_replace(array('+', '/', '='), array('-', '_', '.'), $txt);
    $tmp = '';
    $j = 0;
    $k = 0;
    $tlen = strlen($txt);
    $klen = strlen($mdKey);
    for ($i = 0; $i < $tlen; $i++) {
        $k = $k == $klen ? 0 : $k;
        $j = ($nhnum + strpos($chars, $txt[$i]) + ord($mdKey[$k++])) % 64;
        $tmp .= $chars[$j];
    }
    $tmplen = strlen($tmp);
    $tmp = substr_replace($tmp, $ch3, $nh2 % ++$tmplen, 0);
    $tmp = substr_replace($tmp, $ch2, $nh1 % ++$tmplen, 0);
    $tmp = substr_replace($tmp, $ch1, $knum % ++$tmplen, 0);
    return $tmp;
}

/**
 * 解密函数
 *
 * @param string $txt 需要解密的字符串
 * @param string $key 密匙
 * @return string 字符串类型的返回结果
 */
function decrypt($txt, $key = '', $ttl = 0)
{
    if (empty($txt)) return $txt;
    if (empty($key)) $key = md5(MD5_KEY);

    $chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.";
    $ikey = "-x6g6ZWm2G9g_vr0Bo.pOq3kRIxsZ6rm";
    $knum = 0;
    $i = 0;
    $tlen = @strlen($txt);
    while (isset($key[$i])) $knum += ord($key[$i++]);
    $ch1 = @$txt[$knum % $tlen];
    $nh1 = strpos($chars, $ch1);
    $txt = @substr_replace($txt, '', $knum % $tlen--, 1);
    $ch2 = @$txt[$nh1 % $tlen];
    $nh2 = @strpos($chars, $ch2);
    $txt = @substr_replace($txt, '', $nh1 % $tlen--, 1);
    $ch3 = @$txt[$nh2 % $tlen];
    $nh3 = @strpos($chars, $ch3);
    $txt = @substr_replace($txt, '', $nh2 % $tlen--, 1);
    $nhnum = $nh1 + $nh2 + $nh3;
    $mdKey = substr(md5(md5(md5($key . $ch1) . $ch2 . $ikey) . $ch3), $nhnum % 8, $knum % 8 + 16);
    $tmp = '';
    $j = 0;
    $k = 0;
    $tlen = @strlen($txt);
    $klen = @strlen($mdKey);
    for ($i = 0; $i < $tlen; $i++) {
        $k = $k == $klen ? 0 : $k;
        $j = strpos($chars, $txt[$i]) - $nhnum - ord($mdKey[$k++]);
        while ($j < 0) $j += 64;
        $tmp .= $chars[$j];
    }
    $tmp = str_replace(array('-', '_', '.'), array('+', '/', '='), $tmp);
    $tmp = trim(base64_decode($tmp));

    if (preg_match("/\d{10}_/s", substr($tmp, 0, 11))) {
        if ($ttl > 0 && (time() - substr($tmp, 0, 11) > $ttl)) {
            $tmp = null;
        } else {
            $tmp = substr($tmp, 11);
        }
    }
    return $tmp;
}

/**
 * 取得IP
 *
 *
 * @return string 字符串类型的返回结果
 */
function getIp()
{
    if (@$_SERVER['HTTP_CLIENT_IP'] && $_SERVER['HTTP_CLIENT_IP'] != 'unknown') {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (@$_SERVER['HTTP_X_FORWARDED_FOR'] && $_SERVER['HTTP_X_FORWARDED_FOR'] != 'unknown') {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return preg_match('/^\d[\d.]+\d$/', $ip) ? $ip : '';
}

/**
 * 数据库模型实例化入口
 *
 * @param string $model 模型名称
 * @return obj 对象形式的返回结果
 */
function Model($model = null)
{
    static $_cache = array();
    if (!is_null($model) && isset($_cache[$model])) return $_cache[$model];
    $file_name = BASE_DATA_PATH . '/model/' . $model . '.model.php';
    $class_name = $model . 'Model';
    if (!file_exists($file_name)) {
        return $_cache[$model] =  new Model($model);
    } else {
        require_once($file_name);
        if (!class_exists($class_name)) {
            $error = 'Model Error:  Class ' . $class_name . ' is not exists!';
            throw_exception($error);
        } else {
            return $_cache[$model] = new $class_name();
        }
    }
}

/**
 * 行为模型实例
 *
 * @param string $model 模型名称
 * @return obj 对象形式的返回结果
 */
function Logic($model = null, $base_path = null)
{
    static $_cache = array();
    $cache_key = $model . '.' . $base_path;
    if (!is_null($model) && isset($_cache[$cache_key])) return $_cache[$cache_key];
    $base_path = $base_path == null ? BASE_DATA_PATH : $base_path;
    $file_name = $base_path . '/logic/' . $model . '.logic.php';
    $class_name = $model . 'Logic';
    if (!file_exists($file_name)) {
        return $_cache[$cache_key] =  new Model($model);
    } else {
        require_once($file_name);
        if (!class_exists($class_name)) {
            $error = 'Logic Error:  Class ' . $class_name . ' is not exists!';
            throw_exception($error);
        } else {
            return $_cache[$cache_key] = new $class_name();
        }
    }
}

/**
 * 读取目录列表
 * 不包括 . .. 文件 三部分
 *
 * @param string $path 路径
 * @return array 数组格式的返回结果
 */
function readDirList($path)
{
    if (is_dir($path)) {
        $handle = @opendir($path);
        $dir_list = array();
        if ($handle) {
            while (false !== ($dir = readdir($handle))) {
                if ($dir != '.' && $dir != '..' && is_dir($path . DS . $dir)) {
                    $dir_list[] = $dir;
                }
            }
            return $dir_list;
        } else {
            return false;
        }
    } else {
        return false;
    }
}

/**
 * 转换特殊字符
 *
 * @param string $string 要转换的字符串
 * @return string 字符串类型的返回结果
 */
function replaceSpecialChar($string)
{
    $str = str_replace("\r\n", "", $string);
    $str = str_replace("\t", "    ", $string);
    $str = str_replace("\n", "", $string);
    return $string;
}

/**
 * 编辑器内容
 *
 * @param int $id 编辑器id名称，与name同名
 * @param string $value 编辑器内容
 * @param string $width 宽 带px
 * @param string $height 高 带px
 * @param string $style 样式内容
 * @param string $upload_state 上传状态，默认是开启
 */
function showEditor($id, $value = '', $width = '700px', $height = '300px', $style = 'visibility:hidden;', $upload_state = "true", $media_open = false, $type = 'all')
{
    //是否开启多媒体
    $media = '';
    if ($media_open) {
        $media = ", 'flash', 'media'";
    }
    if (C('subdomain_suffix')) {
        $upload_state = "false";
    }
    switch ($type) {
        case 'basic':
            $items = "['source', '|', 'fullscreen', 'undo', 'redo', 'cut', 'copy', 'paste', '|', 'about']";
            break;
        case 'simple':
            $items = "['source', '|', 'fullscreen', 'undo', 'redo', 'cut', 'copy', 'paste', '|',
            'fontname', 'fontsize', 'forecolor', 'hilitecolor', 'bold', 'italic', 'underline',
            'removeformat', 'justifyleft', 'justifycenter', 'justifyright', 'insertorderedlist',
            'insertunorderedlist', '|', 'emoticons', 'image', 'link', '|', 'about']";
            break;
        default:
            $items = "['source', '|', 'fullscreen', 'undo', 'redo', 'print', 'cut', 'copy', 'paste',
            'plainpaste', 'wordpaste', '|', 'justifyleft', 'justifycenter', 'justifyright',
            'justifyfull', 'insertorderedlist', 'insertunorderedlist', 'indent', 'outdent', 'subscript',
            'superscript', '|', 'selectall', 'clearhtml','quickformat','|',
            'formatblock', 'fontname', 'fontsize', '|', 'forecolor', 'hilitecolor', 'bold',
            'italic', 'underline', 'strikethrough', 'lineheight', 'removeformat', '|', 'image'" . $media . ", 'table', 'hr', 'emoticons', 'link', 'unlink', '|', 'about']";
            break;
    }
    //图片、Flash、视频、文件的本地上传都可开启。默认只有图片，要启用其它的需要修改resource\kindeditor\php下的upload_json.php的相关参数
    echo '<textarea id="' . $id . '" name="' . $id . '" style="width:' . $width . ';height:' . $height . ';' . $style . '">' . $value . '</textarea>';
    echo '
<script src="' . RESOURCE_SITE_URL . '/kindeditor/kindeditor-min.js" charset="utf-8"></script>
<script src="' . RESOURCE_SITE_URL . '/kindeditor/lang/zh_CN.js" charset="utf-8"></script>';
    if ($id == 'goods_package_body') {
        echo '
<script>
    var KE1;
  KindEditor.ready(function(K) {
        KE1 = K.create("textarea[name=\'' . $id . '\']", {
                        items : ' . $items . ',
                        cssPath : "' . RESOURCE_SITE_URL . '/kindeditor/themes/default/default.css",
                        allowImageUpload : ' . $upload_state . ',
                        allowFlashUpload : false,
                        allowMediaUpload : false,
                        allowFileManager : false,
                        syncType:"form",
                        afterCreate : function() {
                            var self = this;
                            self.sync();
                        },
                        afterChange : function() {
                            var self = this;
                            self.sync();
                        },
                        afterBlur : function() {
                            var self = this;
                            self.sync();
                        }
        });
            KE1.appendHtml = function(id,val) {
                this.html(this.html() + val);
                if (this.isCreated) {
                    var cmd = this.cmd;
                    cmd.range.selectNodeContents(cmd.doc.body).collapse(false);
                    cmd.select();
                }
                return this;
            }
    });
</script>
    ';
    } elseif ($id == 'goods_use_body') {
        echo '
<script>
    var KE2;
  KindEditor.ready(function(K) {
        KE2 = K.create("textarea[name=\'' . $id . '\']", {
                        items : ' . $items . ',
                        cssPath : "' . RESOURCE_SITE_URL . '/kindeditor/themes/default/default.css",
                        allowImageUpload : ' . $upload_state . ',
                        allowFlashUpload : false,
                        allowMediaUpload : false,
                        allowFileManager : false,
                        syncType:"form",
                        afterCreate : function() {
                            var self = this;
                            self.sync();
                        },
                        afterChange : function() {
                            var self = this;
                            self.sync();
                        },
                        afterBlur : function() {
                            var self = this;
                            self.sync();
                        }
        });
            KE2.appendHtml = function(id,val) {
                this.html(this.html() + val);
                if (this.isCreated) {
                    var cmd = this.cmd;
                    cmd.range.selectNodeContents(cmd.doc.body).collapse(false);
                    cmd.select();
                }
                return this;
            }
    });
</script>
    ';
    } else {
        echo '
<script>
    var KE;
  KindEditor.ready(function(K) {
        KE = K.create("textarea[name=\'' . $id . '\']", {
                        items : ' . $items . ',
                        cssPath : "' . RESOURCE_SITE_URL . '/kindeditor/themes/default/default.css",
                        allowImageUpload : ' . $upload_state . ',
                        allowFlashUpload : false,
                        allowMediaUpload : false,
                        allowFileManager : false,
                        syncType:"form",
                        afterCreate : function() {
                            var self = this;
                            self.sync();
                        },
                        afterChange : function() {
                            var self = this;
                            self.sync();
                        },
                        afterBlur : function() {
                            var self = this;
                            self.sync();
                        }
        });
            KE.appendHtml = function(id,val) {
                this.html(this.html() + val);
                if (this.isCreated) {
                    var cmd = this.cmd;
                    cmd.range.selectNodeContents(cmd.doc.body).collapse(false);
                    cmd.select();
                }
                return this;
            }
    });
</script>
    ';
    }
    return true;
}

/**
 * 获取目录大小
 *
 * @param string $path 目录
 * @param int $size 目录大小
 * @return int 整型类型的返回结果
 */
function getDirSize($path, $size = 0)
{
    $dir = @dir($path);
    if (!empty($dir->path) && !empty($dir->handle)) {
        while ($filename = $dir->read()) {
            if ($filename != '.' && $filename != '..') {
                if (is_dir($path . DS . $filename)) {
                    $size += getDirSize($path . DS . $filename);
                } else {
                    $size += filesize($path . DS . $filename);
                }
            }
        }
    }
    return $size ? $size : 0;
}

/**
 * 删除缓存目录下的文件或子目录文件
 *
 * @param string $dir 目录名或文件名
 * @return boolean
 */
function delCacheFile($dir)
{
    //防止删除cache以外的文件
    if (strpos($dir, '..') !== false) return false;
    $path = BASE_DATA_PATH . DS . 'cache' . DS . $dir;
    if (is_dir($path)) {
        $file_list = array();
        readFileList($path, $file_list);
        if (!empty($file_list)) {
            foreach ($file_list as $v) {
                if (basename($v) != 'index.html') @unlink($v);
            }
        }
    } else {
        if (basename($path) != 'index.html') @unlink($path);
    }
    return true;
}

/**
 * 获取文件列表(所有子目录文件)
 *
 * @param string $path 目录
 * @param array $file_list 存放所有子文件的数组
 * @param array $ignore_dir 需要忽略的目录或文件
 * @return array 数据格式的返回结果
 */
function readFileList($path, &$file_list, $ignore_dir = array())
{
    $path = rtrim($path, '/');
    if (is_dir($path)) {
        $handle = @opendir($path);
        if ($handle) {
            while (false !== ($dir = readdir($handle))) {
                if ($dir != '.' && $dir != '..') {
                    if (!in_array($dir, $ignore_dir)) {
                        if (is_file($path . DS . $dir)) {
                            $file_list[] = $path . DS . $dir;
                        } elseif (is_dir($path . DS . $dir)) {
                            readFileList($path . DS . $dir, $file_list, $ignore_dir);
                        }
                    }
                }
            }
            @closedir($handle);
        } else {
            return false;
        }
    } else {
        return false;
    }
}

/**
 * 价格格式化
 *
 * @param int    $price
 * @return string    $price_format
 */
function ncPriceFormat($price)
{
    return number_format($price, 2, '.', '');
}

/**
 * 价格格式化
 *
 * @param int    $price
 * @return string    $price_format
 */
function ncPriceFormatForList($price)
{
    if ($price >= 10000) {
        return number_format(floor($price / 100) / 100, 2, '.', '') . '万';
    } else {
        return '&yen;' . ncPriceFormat($price);
    }
}

/**
 * 二级域名解析
 * @return int 店铺id
 */
function subdomain()
{
    $store_id = 0;
    /**
     * 获得系统配置,二级域名功能是否开启
     */
    if (C('enabled_subdomain') == '1') { //开启了二级域名
        $line = @explode(SUBDOMAIN_SUFFIX, $_SERVER['HTTP_HOST']);
        $line = trim($line[0], '.');
        if (empty($line) || strtolower($line) == 'www') return 0;

        $model_store = Model('store');
        $store_info = $model_store->getStoreInfo(array('store_domain' => $line));
        //二级域名存在
        if ($store_info['store_id'] > 0) {
            $store_id = $store_info['store_id'];
            $_GET['store_id'] = $store_info['store_id'];
        }
    }
    return $store_id;
}

/**
 * 通知邮件/通知消息 内容转换函数
 *
 * @param string $message 内容模板
 * @param array $param 内容参数数组
 * @return string 通知内容
 */
function ncReplaceText($message, $param)
{
    if (!is_array($param)) return false;
    foreach ($param as $k => $v) {
        if (is_array($v)) {
            continue;
        }
        $message    = str_replace('{$' . $k . '}', $v, $message);
    }
    return $message;
}

/**
 * 字符串切割函数，一个字母算一个位置,一个字算2个位置
 *
 * @param string $string 待切割的字符串
 * @param int $length 切割长度
 * @param string $dot 尾缀
 */
function str_cut($string, $length, $dot = '')
{
    $string = str_replace(array('&nbsp;', '&amp;', '&quot;', '&#039;', '&ldquo;', '&rdquo;', '&mdash;', '&lt;', '&gt;', '&middot;', '&hellip;'), array(' ', '&', '"', "'", '“', '”', '—', '<', '>', '·', '…'), $string);
    $strlen = strlen($string);
    if ($strlen <= $length) return $string;
    $maxi = $length - strlen($dot);
    $strcut = '';
    if (strtolower(CHARSET) == 'utf-8') {
        $n = $tn = $noc = 0;
        while ($n < $strlen) {
            $t = ord($string[$n]);
            if ($t == 9 || $t == 10 || (32 <= $t && $t <= 126)) {
                $tn = 1;
                $n++;
                $noc++;
            } elseif (194 <= $t && $t <= 223) {
                $tn = 2;
                $n += 2;
                $noc += 2;
            } elseif (224 <= $t && $t < 239) {
                $tn = 3;
                $n += 3;
                $noc += 2;
            } elseif (240 <= $t && $t <= 247) {
                $tn = 4;
                $n += 4;
                $noc += 2;
            } elseif (248 <= $t && $t <= 251) {
                $tn = 5;
                $n += 5;
                $noc += 2;
            } elseif ($t == 252 || $t == 253) {
                $tn = 6;
                $n += 6;
                $noc += 2;
            } else {
                $n++;
            }
            if ($noc >= $maxi) break;
        }
        if ($noc > $maxi) $n -= $tn;
        $strcut = substr($string, 0, $n);
    } else {
        $dotlen = strlen($dot);
        $maxi = $length - $dotlen;
        for ($i = 0; $i < $maxi; $i++) {
            $strcut .= ord($string[$i]) > 127 ? $string[$i] . $string[++$i] : $string[$i];
        }
    }
    $strcut = str_replace(array('&', '"', "'", '<', '>'), array('&amp;', '&quot;', '&#039;', '&lt;', '&gt;'), $strcut);
    return $strcut . $dot;
}

/**
 * unicode转为utf8
 * @param string $str 待转的字符串
 * @return string
 */
function unicodeToUtf8($str, $order = "little")
{
    $utf8string = "";
    $n = strlen($str);
    for ($i = 0; $i < $n; $i++) {
        if ($order == "little") {
            $val = str_pad(dechex(ord($str[$i + 1])), 2, 0, STR_PAD_LEFT) .
                str_pad(dechex(ord($str[$i])),      2, 0, STR_PAD_LEFT);
        } else {
            $val = str_pad(dechex(ord($str[$i])),      2, 0, STR_PAD_LEFT) .
                str_pad(dechex(ord($str[$i + 1])), 2, 0, STR_PAD_LEFT);
        }
        $val = intval($val, 16); // 由于上次的.连接，导致$val变为字符串，这里得转回来。
        $i++; // 两个字节表示一个unicode字符。
        $c = "";
        if ($val < 0x7F) { // 0000-007F
            $c .= chr($val);
        } elseif ($val < 0x800) { // 0080-07F0
            $c .= chr(0xC0 | ($val / 64));
            $c .= chr(0x80 | ($val % 64));
        } else { // 0800-FFFF
            $c .= chr(0xE0 | (($val / 64) / 64));
            $c .= chr(0x80 | (($val / 64) % 64));
            $c .= chr(0x80 | ($val % 64));
        }
        $utf8string .= $c;
    }
    /* 去除bom标记 才能使内置的iconv函数正确转换 */
    if (ord(substr($utf8string, 0, 1)) == 0xEF && ord(substr($utf8string, 1, 2)) == 0xBB && ord(substr($utf8string, 2, 1)) == 0xBF) {
        $utf8string = substr($utf8string, 3);
    }
    return $utf8string;
}

/*
 * 重写$_SERVER['REQUREST_URI']
 */
function request_uri()
{
    if (isset($_SERVER['REQUEST_URI'])) {
        $uri = $_SERVER['REQUEST_URI'];
    } else {
        if (isset($_SERVER['argv'])) {
            $uri = $_SERVER['PHP_SELF'] . '?' . $_SERVER['argv'][0];
        } else {
            $uri = $_SERVER['PHP_SELF'] . '?' . $_SERVER['QUERY_STRING'];
        }
    }
    $uri = explode('/', $uri);
    $uri = end($uri);

    return rtrim(defined('APP_SITE_URL') ? APP_SITE_URL : '', '/') . '/' . $uri;
}

// 记录和统计时间（微秒）
function addUpTime($start, $end = '', $dec = 3)
{
    static $_info = array();
    if (!empty($end)) { // 统计时间
        if (!isset($_info[$end])) {
            $_info[$end]   =  microtime(TRUE);
        }
        return number_format(($_info[$end] - $_info[$start]), $dec);
    } else { // 记录时间
        $_info[$start]  =  microtime(TRUE);
    }
}

/**
 * 取得系统配置信息
 *
 * @param string $key 取得下标值
 * @return mixed
 */
function C($key)
{
    //    if ($key == 'debug') {
    //        return 1;
    //    }
    if (strpos($key, '.')) {
        $key = explode('.', $key);
        $value = \Shopnc\Core::getConfig($key[0]);
        if (isset($key[2])) {
            return $value[$key[1]][$key[2]];
        } else {
            return $value[$key[1]];
        }
    } else {
        return \Shopnc\Core::getConfig($key);
    }
}

/**
 * 取得商品默认大小图片
 *
 * @param string $key   图片大小 small tiny
 * @return string
 */
function defaultGoodsImage($key)
{
    $file = str_ireplace('.', '_' . $key . '.', C('default_goods_image'));
    return ATTACH_COMMON . DS . $file;
}

/**
 * 取得商品默认大小视频
 *
 * @return string
 */
function defaultGoodsVideo()
{
    $file = str_ireplace('.', '.',  C('default_goods_video'));
    return ATTACH_COMMON . DS . $file;
}

/**
 * 取得用户头像图片
 *
 * @param string $member_avatar
 * @return string
 */
function getMemberAvatar($member_avatar)
{
    if (empty($member_avatar)) {
        return UPLOAD_SITE_URL . DS . ATTACH_COMMON . DS . C('default_user_portrait');
    } else {
        if (file_exists(BASE_UPLOAD_PATH . DS . ATTACH_AVATAR . DS . $member_avatar)) {
            return UPLOAD_SITE_URL . DS . ATTACH_AVATAR . DS . $member_avatar;
        } else {
            return UPLOAD_SITE_URL . DS . ATTACH_COMMON . DS . C('default_user_portrait');
        }
    }
}
/**
 * 取得用户头像图片
 *
 * @param string $member_avatar
 * @return string
 */
function getMemberAvatarHttps($member_avatar)
{
    if (empty($member_avatar)) {
        return UPLOAD_SITE_URL_HTTPS . DS . ATTACH_COMMON . DS . C('default_user_portrait');
    } else {
        if (file_exists(BASE_UPLOAD_PATH . DS . ATTACH_AVATAR . DS . $member_avatar)) {
            return UPLOAD_SITE_URL_HTTPS . DS . ATTACH_AVATAR . DS . $member_avatar;
        } else {
            return UPLOAD_SITE_URL_HTTPS . DS . ATTACH_COMMON . DS . C('default_user_portrait');
        }
    }
}
/**
 * 成员头像
 * @param string $member_id
 * @return string
 */
function getMemberAvatarForID($id)
{
    if (file_exists(BASE_UPLOAD_PATH . '/' . ATTACH_AVATAR . '/avatar_' . $id . '.jpg')) {
        return UPLOAD_SITE_URL . '/' . ATTACH_AVATAR . '/avatar_' . $id . '.jpg';
    } else {
        return UPLOAD_SITE_URL . '/' . ATTACH_COMMON . DS . C('default_user_portrait');
    }
}
/**
 * 取得店铺标志
 *
 * @param string $img 图片名
 * @param string $type 查询类型 store_logo/store_avatar
 * @return string
 */
function getStoreLogo($img, $type = 'store_avatar')
{
    if ($type == 'store_avatar') {
        if (empty($img)) {
            return UPLOAD_SITE_URL . DS . ATTACH_COMMON . DS . C('default_store_avatar');
        } else {
            return UPLOAD_SITE_URL . DS . ATTACH_STORE . DS . $img;
        }
    } elseif ($type == 'store_logo') {
        if (empty($img)) {
            return UPLOAD_SITE_URL . DS . ATTACH_COMMON . DS . C('default_store_logo');
        } else {
            return UPLOAD_SITE_URL . DS . ATTACH_STORE . DS . $img;
        }
    }
}

/**
 * 获取文章URL
 */
function getCMSArticleUrl($article_id)
{
    if (URL_MODEL) {
        // 开启伪静态
        return CMS_SITE_URL . DS . 'article-' . $article_id . '.html';
    } else {
        return CMS_SITE_URL . DS . 'index.php?act=article&op=article_detail&article_id=' . $article_id;
    }
}

/**
 * 获取画报URL
 */
function getCMSPictureUrl($picture_id)
{
    if (URL_MODEL) {
        // 开启伪静态
        return CMS_SITE_URL . DS . 'picture-' . $picture_id . '.html';
    } else {
        return CMS_SITE_URL . DS . 'index.php?act=picture&op=picture_detail&picture_id=' . $picture_id;
    }
}

/**
 * 获取文章图片URL
 */
function getCMSArticleImageUrl($image_path, $image_name, $type = 'list')
{
    if (empty($image_name)) {
        return UPLOAD_SITE_URL . DS . ATTACH_CMS . DS . 'no_cover.png';
    } else {
        $image_array = unserialize($image_name);
        if (!empty($image_array['name'])) {
            $image_name = $image_array['name'];
        }
        if (!empty($image_array['path'])) {
            $image_path = $image_array['path'];
        }
        $ext_array = array('list', 'max');
        $file_path = ATTACH_CMS . DS . 'article' . DS . $image_path . DS . str_ireplace('.', '_' . $type . '.', $image_name);
        if (file_exists(BASE_UPLOAD_PATH . DS . $file_path)) {
            $image_name = UPLOAD_SITE_URL . DS . $file_path;
        } else {
            $image_name = UPLOAD_SITE_URL . DS . ATTACH_CMS . DS . 'no_cover.png';
        }
        return $image_name;
    }
}

/**
 * 获取文章图片URL
 */
function getCMSImageName($image_name_string)
{
    $image_array = unserialize($image_name_string);
    if (!empty($image_array['name'])) {
        $image_name = $image_array['name'];
    } else {
        $image_name = $image_name_string;
    }
    return $image_name;
}

/**
 * 获取CMS专题图片
 */
function getCMSSpecialImageUrl($image_name = '')
{
    return UPLOAD_SITE_URL . DS . ATTACH_CMS . DS . 'special' . DS . $image_name;
}

/**
 * 获取CMS专题路径
 */
function getCMSSpecialImagePath($image_name = '')
{
    return BASE_UPLOAD_PATH . DS . ATTACH_CMS . DS . 'special' . DS . $image_name;
}

/**
 * 获取CMS首页图片
 */
function getCMSIndexImageUrl($image_name = '')
{
    return UPLOAD_SITE_URL . DS . ATTACH_CMS . DS . 'index' . DS . $image_name;
}

/**
 * 获取CMS首页图片路径
 */
function getCMSIndexImagePath($image_name = '')
{
    return BASE_UPLOAD_PATH . DS . ATTACH_CMS . DS . 'index' . DS . $image_name;
}

/**
 * 获取CMS专题Url
 */
function getCMSSpecialUrl($special_id)
{
    return CMS_SITE_URL . DS . 'index.php?act=special&op=special_detail&special_id=' . $special_id;
}

/**
 * 获取商城专题Url
 */
function getShopSpecialUrl($special_id)
{
    if (URL_MODEL) {
        $url = base64_encode('special_detail-' . $special_id);
        return  SHOP_SITE_URL . DS . $url . '.html';
        // return  SHOP_SITE_URL.DS.'special_detail-'.$special_id.'.html';
    }
    return SHOP_SITE_URL . DS . 'index.php?act=special&op=special_detail&special_id=' . $special_id;
}


/**
 * 获取CMS专题静态文件
 */
function getCMSSpecialHtml($special_id)
{
    $url = BASE_UPLOAD_PATH . DS . ATTACH_CMS . DS . 'special_html' . DS . md5('special' . intval($special_id)) . '.html';
    $special_file = file_get_contents($url);
    return $special_file;
}

/**
 * 获取微商城个人秀图片地址
 */
function getMicroshopPersonalImageUrl($personal_info, $type = '')
{
    $ext_array = array('list', 'tiny');
    $personal_image_array = array();
    $personal_image_list = explode(',', $personal_info['commend_image']);
    if (!empty($personal_image_list)) {
        foreach ($personal_image_list as $value) {
            if (!empty($type) && in_array($type, $ext_array)) {
                $file_name = str_replace('.', '_' . $type . '.', $value);
            } else {
                $file_name = $value;
            }
            $file_path = $personal_info['commend_member_id'] . DS . $file_name;
            if (is_file(BASE_UPLOAD_PATH . DS . ATTACH_MICROSHOP . DS . $file_path)) {
                $personal_image_array[] = UPLOAD_SITE_URL . DS . ATTACH_MICROSHOP . DS . $file_path;
            } else {
                $personal_image_array[] = getMicroshopDefaultImage();
            }
        }
    } else {
        $personal_image_array[] = getMicroshopDefaultImage();
    }
    return $personal_image_array;
}

function getMicroshopDefaultImage()
{
    return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
}

/**
 * 获取开店申请图片
 */
function getStoreJoininImageUrl($image_name = '')
{
    return UPLOAD_SITE_URL . DS . ATTACH_STORE_JOININ . DS . $image_name;
}

/**
 * 获取实名认证身份证图片
 */
function getMemberIDCardImageUrl($image_name = '')
{
    return UPLOAD_SITE_URL . DS . ATTACH_CERTIFICATION . DS . $image_name;
}

/**
 * 获取开店装修图片地址
 */
function getStoreDecorationImageUrl($image_name = '', $store_id = null)
{
    if (empty($store_id)) {
        $image_name_array = explode('_', $image_name);
        $store_id = $image_name_array[0];
    }

    $image_path = DS . ATTACH_STORE_DECORATION . DS . $store_id . DS . $image_name;
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return '';
    }
}

/**
 * 获取运单图片地址
 */
function getMbSpecialImageUrl($image_name = '')
{
    $name_array = explode('_', $image_name);
    if (count($name_array) == 2) {
        $image_path = DS . ATTACH_MOBILE . DS . 'special' . DS . $name_array[0] . DS . $image_name;
    } else {
        $image_path = DS . ATTACH_MOBILE . DS . 'special' . DS . $image_name;
    }
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }
}

/**
 * 获取视频列表页广告图地址
 */
function getMbFocusImageUrl($image_name = '')
{
    $image_path = DS . ATTACH_MOBILE . DS . 'video_focus' . DS . $image_name;
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }
}

/**
 * 获取资讯列表图片地址
 */
function getMbNewsImageUrl($image_name = '')
{
    $image_path = DS . ATTACH_MOBILE . DS . 'video_news' . DS . $image_name;
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }
}

/**
 * 获取点播视频地址
 */
function getMbDemandVideoUrl($video_name = '', $upload_type = '')
{
    if ($upload_type == 'promotefile') {
        $video_path = DS . ATTACH_MOBILE . DS . 'video_promote/video' . DS . $video_name;
    } elseif ($upload_type == 'demandfile') {
        $video_path = DS . ATTACH_MOBILE . DS . 'video_demand' . DS . $video_name;
    }
    if (is_file(BASE_UPLOAD_PATH . $video_path)) {
        return UPLOAD_SITE_URL . $video_path;
    } else {
        return '';
    }
}

/**
 * 获取点播图片地址
 */
function getMbDemandImageUrl($image_name = '')
{
    $image_path = DS . ATTACH_MOBILE . DS . 'video_promote/image' . DS . $image_name;
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }
}

/**
 * 获取视频分类地址
 */
function getMbVideoCateImageUrl($image_name = '')
{
    $image_path = DS . ATTACH_MOBILE . DS . 'video_cate' . DS . $image_name;
    if (is_file(BASE_UPLOAD_PATH . $image_path)) {
        return UPLOAD_SITE_URL . $image_path;
    } else {
        return UPLOAD_SITE_URL . '/' . defaultGoodsImage('240');
    }
}

/**
 * 加载文件
 *
 * 使用require_once函数，只适用于加载框架内类库文件
 * 如果文件名中包含"_"使用"#"代替
 *
 * @example import('cache'); //require_once(BASE_PATH.'/framework/libraries/cache.php');
 * @example import('libraries.cache');  //require_once(BASE_PATH.'/framework/libraries/cache.php');
 * @example import('function.core');    //require_once(BASE_PATH.'/framework/function/core.php');
 * @example import('.control.adv')  //require_once(BASE_PATH.'/control/adv.php');
 *
 * @param 要加载的文件 $libname
 * @param 文件扩展名 $file_ext
 */
function import($libname, $file_ext = '.php')
{
    //替换为目录符号/
    if (strstr($libname, '.')) {
        $path = str_replace('.', '/', $libname);
    } else {
        $path = 'libraries/' . $libname;
    }
    // 基准目录，如果是顶级目录
    if (substr($libname, 0, 1) == '.') {
        $base_dir = BASE_CORE_PATH . '/';
        $path = ltrim(str_replace('libraries/', '', $path), '/');
    } else {
        $base_dir = BASE_CORE_PATH . '/framework/';
    }
    //如果文件名中含有.使用#代替
    if (strstr($path, '#')) {
        $path = str_replace('#', '.', $path);
    }
    //返回安全路径
    if (preg_match('/^[\w\d\/_.]+$/i', $path)) {
        $file = realpath($base_dir . $path . $file_ext);
    } else {
        $file = false;
    }
    if (!$file) {
        exit($path . $file_ext . ' isn\'t exists!');
    } else {
        require_once($file);
    }
}

/**
 * 取得随机数
 *
 * @param int $length 生成随机数的长度
 * @param int $numeric 是否只产生数字随机数 1是0否
 * @return string
 */
function random($length, $numeric = 0)
{
    $seed = base_convert(md5(microtime() . $_SERVER['DOCUMENT_ROOT']), 16, $numeric ? 10 : 35);
    $seed = $numeric ? (str_replace('0', '', $seed) . '012340567890') : ($seed . 'zZ' . strtoupper($seed));
    $hash = '';
    $max = strlen($seed) - 1;
    for ($i = 0; $i < $length; $i++) {
        $hash .= $seed[mt_rand(0, $max)];
    }
    return $hash;
}

/**
 * 返回模板文件所在完整目录
 *
 * @param str $tplpath
 * @return string
 */
function template($tplpath)
{
    if (strpos($tplpath, ':') !== false) {
        $tpltmp = explode(':', $tplpath);
        return BASE_DATA_PATH . '/' . $tpltmp[0] . '/tpl/' . $tpltmp[1] . '.php';
    } else {
        if (defined('MODULE_NAME')) {
            return MODULES_BASE_PATH . '/templates/' . TPL_NAME . '/' . $tplpath . '.php';
        } else {
            return BASE_PATH . '/templates/' . TPL_NAME . '/' . $tplpath . '.php';
        }
    }
}

/**
 * 检测FORM是否提交
 * @param  $check_token 是否验证token
 * @param  $check_captcha 是否验证验证码
 * @param  $return_type 'alert','num'
 * @return boolean
 */
function chksubmit($check_token = false, $check_captcha = false, $return_type = 'alert')
{
    $submit = isset($_POST['form_submit']) ? $_POST['form_submit'] : $_GET['form_submit'];
    //redis记录请求来源的ip登录次数，如果1分钟内请求三次则返回错误
    if ($submit != 'ok') return false;
    if ($_GET['act'] == 'login' && $_GET['op'] == 'login') {
        $ip = getIp();
        $login_key = 'login_limit:' . $ip;
        $count = Redis::get($login_key);
        if ($count >= 3) {
            showDialog('验证太频繁，稍后再试');
        }
        Redis::set($login_key, $count + 1, 60);
    }


    if ($check_token && !Security::checkToken()) {
        if ($return_type == 'alert') {
            showDialog('Token error!');
        } else {
            return -11;
        }
    }
    if ($check_captcha) {
        if (!checkSeccode($_POST['nchash'], $_POST['captcha'])) {
            setNcCookie('seccode' . $_POST['nchash'], '', -3600);
            if ($return_type == 'alert') {
                showDialog('验证码错误!');
            } else {
                return -12;
            }
        }
        setNcCookie('seccode' . $_POST['nchash'], '', -3600);
    }
    return true;
}

/**
 * sns表情标示符替换为html
 */
function parsesmiles($message)
{
    $smilescache_file = BASE_DATA_PATH . DS . 'smilies' . DS . 'smilies.php';
    if (file_exists($smilescache_file)) {
        include $smilescache_file;
        if (strtoupper(CHARSET) == 'GBK') {
            $smilies_array = Language::getGBK($message);
        }
        if (!empty($smilies_array) && is_array($smilies_array)) {
            $imagesurl = RESOURCE_SITE_URL . DS . 'js' . DS . 'smilies' . DS . 'images' . DS;
            $replace_arr = array();
            foreach ($smilies_array['replacearray'] as $key => $smiley) {
                $replace_arr[$key] = '<img src="' . $imagesurl . $smiley['imagename'] . '" title="' . $smiley['desc'] . '" border="0" alt="' . $imagesurl . $smiley['desc'] . '" />';
            }

            $message = preg_replace($smilies_array['searcharray'], $replace_arr, $message);
        }
    }
    return $message;
}

/**
 * 输出validate的验证信息
 *
 * @param array/string $error
 */
function showValidateError($error)
{
    if (!empty($_GET['inajax'])) {
        foreach (explode('<br/>', $error) as $v) {
            if (trim($v != '')) {
                showDialog($v, '', 'error', '', 3);
            }
        }
    } else {
        showDialog($error, '', 'error', '', 3);
    }
}

/**
 * 延时加载分页功能，判断是否有更多连接和limitstart值和经过验证修改的$delay_eachnum值
 * @param int $delay_eachnum 延时分页每页显示的条数
 * @param int $delay_page 延时分页当前页数
 * @param int $count 总记录数
 * @param bool $ispage 是否在分页模式中实现延时分页(前台显示的两种不同效果)
 * @param int $page_nowpage 分页当前页数
 * @param int $page_eachnum 分页每页显示条数
 * @param int $page_limitstart 分页初始limit值
 * @return array array('hasmore'=>'是否显示更多连接','limitstart'=>'加载的limit开始值','delay_eachnum'=>'经过验证修改的$delay_eachnum值');
 */
function lazypage($delay_eachnum, $delay_page, $count, $ispage = false, $page_nowpage = 1, $page_eachnum = 1, $page_limitstart = 1)
{
    //是否有多余
    $hasmore = true;
    $limitstart = 0;
    if ($ispage == true) {
        if ($delay_eachnum < $page_eachnum) { //当延时加载每页条数小于分页的每页条数时候实现延时加载，否则按照普通分页程序流程处理
            $page_totlepage = ceil($count / $page_eachnum);
            //计算limit的开始值
            $limitstart = $page_limitstart + ($delay_page - 1) * $delay_eachnum;
            if ($page_totlepage > $page_nowpage) { //当前不为最后一页
                if ($delay_page >= $page_eachnum / $delay_eachnum) {
                    $hasmore = false;
                }
                //判断如果分页的每页条数与延时加载每页的条数不能整除的处理
                if ($hasmore == false && $page_eachnum % $delay_eachnum > 0) {
                    $delay_eachnum = $page_eachnum % $delay_eachnum;
                }
            } else { //当前最后一页
                $showcount = ($page_totlepage - 1) * $page_eachnum + $delay_eachnum * $delay_page; //已经显示的记录总数
                if ($count <= $showcount) {
                    $hasmore = false;
                }
            }
        } else {
            $hasmore = false;
        }
    } else {
        if ($count <= $delay_page * $delay_eachnum) {
            $hasmore = false;
        }
        //计算limit的开始值
        $limitstart = ($delay_page - 1) * $delay_eachnum;
    }

    return array('hasmore' => $hasmore, 'limitstart' => $limitstart, 'delay_eachnum' => $delay_eachnum);
}

/**
 * 文件数据读取和保存 字符串、数组
 *
 * @param string $name 文件名称（不含扩展名）
 * @param mixed $value 待写入文件的内容
 * @param string $path 写入cache的目录
 * @param string $ext 文件扩展名
 * @return mixed
 */
function F($name, $value = null, $path = 'cache', $ext = '.php')
{
    if (strtolower(substr($path, 0, 5)) == 'cache') {
        $path  = 'data/' . $path;
    }
    static $_cache = array();
    if (isset($_cache[$name . $path])) return $_cache[$name . $path];
    $filename = BASE_ROOT_PATH . '/' . $path . '/' . $name . $ext;
    if (!is_null($value)) {
        $dir = dirname($filename);
        if (!is_dir($dir)) mkdir($dir);
        return write_file($filename, $value);
    }

    if (is_file($filename)) {
        $_cache[$name . $path] = $value = include $filename;
    } else {
        $value = false;
    }
    return $value;
}

/**
 * 内容写入文件
 *
 * @param string $filepath 待写入内容的文件路径
 * @param string/array $data 待写入的内容
 * @param  string $mode 写入模式，如果是追加，可传入“append”
 * @return bool
 */
function write_file($filepath, $data, $mode = null)
{
    if (!is_array($data) && !is_scalar($data)) {
        return false;
    }

    $data = var_export($data, true);

    $data = "<?php defined('InShopNC') or exit('Access Invalid!'); return " . $data . ";";
    $mode = $mode == 'append' ? FILE_APPEND : null;
    if (false === file_put_contents($filepath, ($data), $mode)) {
        return false;
    } else {
        return true;
    }
}

/**
 * 循环创建目录
 *
 * @param string $dir 待创建的目录
 * @param  $mode 权限
 * @return boolean
 */
function mk_dir($dir, $mode = '0777')
{
    if (is_dir($dir) || @mkdir($dir, $mode))
        return true;
    if (!mk_dir(dirname($dir), $mode))
        return false;
    return @mkdir($dir, $mode);
}

/**
 * 封装分页操作到函数，方便调用
 *
 * @param string $cmd 命令类型
 * @param mixed $arg 参数
 * @return mixed
 */
function pagecmd($cmd = '', $arg = '', $page_now = 0)
{
    if (!class_exists('page'))  import('page');
    static $page;
    if ($page == null) {

        $page = new Page();
    }



    switch (strtolower($cmd)) {
        case 'setNowPage':
            $page->setNowPage($arg);
            break;
        case 'seteachnum':
            $page->setEachNum($arg);
            break;
        case 'settotalnum':
            $page->setTotalNum($arg);
            break;
        case 'setstyle':
            $page->setStyle($arg);
            break;
        case 'show':
            return $page->show($arg, $page_now);
            break;
        case 'obj':
            return $page;
            break;
        case 'gettotalnum':
            return $page->getTotalNum();
            break;
        case 'gettotalpage':
            return $page->getTotalPage();
            break;
        case 'getnowpage':
            return $page->getNowPage();
            break;
        case 'settotalpagebynum':
            return $page->setTotalPageByNum($arg);
            break;
        default:
            break;
    }
}

/**
 * 抛出异常
 *
 * @param string $error 异常信息
 */
function throw_exception($error)
{
    if (!defined('IGNORE_EXCEPTION')) {
        showMessage($error, '', 'exception');
    } else {
        Log::record($error);
        exit();
    }
}

/**
 * 输出错误信息
 *
 * @param string $error 错误信息
 */
function halt($error)
{
    showMessage($error, '', 'exception');
}

/**
 * 去除代码中的空白和注释
 *
 * @param string $content 待压缩的内容
 * @return string
 */
function compress_code($content)
{
    $stripStr = '';
    //分析php源码
    $tokens = token_get_all($content);
    $last_space = false;
    for ($i = 0, $j = count($tokens); $i < $j; $i++) {
        if (is_string($tokens[$i])) {
            $last_space = false;
            $stripStr .= $tokens[$i];
        } else {
            switch ($tokens[$i][0]) {
                case T_COMMENT: //过滤各种PHP注释
                case T_DOC_COMMENT:
                    break;
                case T_WHITESPACE:  //过滤空格
                    if (!$last_space) {
                        $stripStr .= ' ';
                        $last_space = true;
                    }
                    break;
                default:
                    $last_space = false;
                    $stripStr .= $tokens[$i][1];
            }
        }
    }
    return $stripStr;
}

/**
 * 取得对象实例
 *
 * @param object $class
 * @param string $method
 * @param array $args
 * @return object
 */
function get_obj_instance($class, $method = '', $args = array())
{
    static $_cache = array();
    $key = $class . $method . (empty($args) ? null : md5(serialize($args)));
    if (isset($_cache[$key])) {
        return $_cache[$key];
    } else {
        if (class_exists($class)) {
            $obj = new $class;
            if (method_exists($obj, $method)) {
                if (empty($args)) {
                    $_cache[$key] = $obj->$method();
                } else {
                    $_cache[$key] = call_user_func_array(array(&$obj, $method), $args);
                }
            } else {
                $_cache[$key] = $obj;
            }
            return $_cache[$key];
        } else {
            throw_exception('Class ' . $class . ' isn\'t exists!');
        }
    }
}

/**
 * 返回以原数组某个值为下标的新数据
 *
 * @param array $array
 * @param string $key
 * @param int $type 1一维数组2二维数组
 * @return array
 */
function array_under_reset($array, $key, $type = 1)
{
    if (is_array($array)) {
        $tmp = array();
        foreach ($array as $v) {
            if ($type === 1) {
                $tmp[$v[$key]] = $v;
            } elseif ($type === 2) {
                $tmp[$v[$key]][] = $v;
            }
        }
        return $tmp;
    } else {
        return $array;
    }
}

/**
 * KV缓存 读
 *
 * @param string $key 缓存名称
 * @param boolean $callback 缓存读取失败时是否使用回调 true代表使用cache.model中预定义的缓存项 默认不使用回调
 * @param callable $callback 传递非boolean值时 通过is_callable进行判断 失败抛出异常 成功则将$key作为参数进行回调
 * @return mixed
 */
function rkcache($key, $callback = false)
{
    if (C('cache_open')) {
        $cacher = \Shopnc\Core::$instances['cacheredis'];
    } else {
        $cacher = Cache::getInstance('file', null);
    }
    if (!$cacher) {
        throw new Exception('Cannot fetch cache object!');
    }

    $value = $cacher->get($key);

    if ($value === false && $callback !== false) {
        if ($callback === true) {
            $callback = array(Model('cache'), 'call');
        }

        if (!is_callable($callback)) {
            throw new Exception('Invalid rkcache callback!');
        }

        $value = call_user_func($callback, $key);
        wkcache($key, $value);
    }

    return $value;
}

/**
 * KV缓存 写
 *
 * @param string $key 缓存名称
 * @param mixed $value 缓存数据 若设为否 则下次读取该缓存时会触发回调（如果有）
 * @param int $expire 缓存时间 单位秒 null代表不过期
 * @return boolean
 */
function wkcache($key, $value, $expire = null)
{
    if (C('cache_open')) {
        $cacher = \Shopnc\Core::$instances['cacheredis'];
    } else {
        $cacher = Cache::getInstance('file', null);
    }
    if (!$cacher) {
        throw new Exception('Cannot fetch cache object!');
    }

    return $cacher->set($key, $value, null, $expire);
}

/**
 * KV缓存 删
 *
 * @param string $key 缓存名称
 * @return boolean
 */
function dkcache($key)
{
    if (C('cache_open')) {
        $cacher = \Shopnc\Core::$instances['cacheredis'];
    } else {
        $cacher = Cache::getInstance('file', null);
    }
    if (!$cacher) {
        throw new Exception('Cannot fetch cache object!');
    }

    return $cacher->rm($key);
}

/**
 * 读取缓存信息
 *
 * @param string $key 要取得缓存键
 * @param string $prefix 键值前缀
 * @param string $fields 所需要的字段
 * @return array/bool
 */
function rcache($key = null, $prefix = '', $fields = '*')
{
    if ($key === null || !C('cache_open')) return array();
    if (!in_array($prefix, array('adv', 'special', 'all_categories', 'area', 'channel', 'class_tag', 'contractitem', 'gc_class', 'goods_class_seo', 'index/article', 'nav', 'own_shop_ids', 'seo', 'setting', 'setting_updates'))) {
        return array();
    }
    $ins = \Shopnc\Core::$instances['cacheredis'];
    $cache_info = $ins->hget($key, $prefix, $fields);
    if ($cache_info === false) {
        //取单个字段且未被缓存
        $data  = array();
    } elseif (is_array($cache_info)) {
        //如果有一个键值为false(即未缓存)，则整个函数返回空，让系统重新生成全部缓存
        $data = $cache_info;
        foreach ($cache_info as $k => $v) {
            if ($v === false) {
                $data = array();
                break;
            }
        }
    } else {
        //string 取单个字段且被缓存
        $data = array($fields => $cache_info);
    }
    // 验证缓存是否过期
    if (isset($data['cache_expiration_time']) && $data['cache_expiration_time'] < TIMESTAMP) {
        $data = array();
    }
    return $data;
}

/**
 * 写入缓存
 *
 * @param string $key 缓存键值
 * @param array $data 缓存数据
 * @param string $prefix 键值前缀
 * @param int $period 缓存周期  单位分，0为永久缓存
 * @return bool 返回值
 */
function wcache($key = null, $data = array(), $prefix = '', $period = 0)
{
    if ($key === null || !C('cache_open') || !is_array($data)) return;
    if (!in_array($prefix, array('adv', 'special', 'all_categories', 'area', 'channel', 'class_tag', 'contractitem', 'gc_class', 'goods_class_seo', 'index/article', 'nav', 'own_shop_ids', 'seo', 'setting', 'setting_updates'))) {
        return;
    }
    $period = intval($period);
    if ($period != 0) {
        $data['cache_expiration_time'] = TIMESTAMP + $period * 60;
    }
    $ins = \Shopnc\Core::$instances['cacheredis'];
    $ins->hset($key, $prefix, $data);
    $cache_info = $ins->hget($key, $prefix);
    return true;
}

/**
 * 删除缓存
 * @param string $key 缓存键值
 * @param string $prefix 键值前缀
 * @return boolean
 */
function dcache($key = null, $prefix = '')
{
    if ($key === null || !C('cache_open')) return true;
    if (!in_array($prefix, array('adv', 'special', 'all_categories', 'area', 'channel', 'class_tag', 'contractitem', 'gc_class', 'goods_class_seo', 'index/article', 'nav', 'own_shop_ids', 'seo', 'setting', 'setting_updates'))) {
        return true;
    }
    $ins = \Shopnc\Core::$instances['cacheredis'];
    return $ins->hdel($key, $prefix);
}

/**
 * 调用推荐位
 *
 * @param int $rec_id 推荐位ID
 * @return string 推荐位内容
 */
function rec($rec_id = null)
{
    import('function.rec_position');
    return rec_position($rec_id);
}

/**
 * 快速调用语言包
 *
 * @param string $key
 * @return string
 */
function L($key = '')
{
    if (class_exists('Language')) {
        if (strpos($key, ',') !== false) {
            $tmp = explode(',', $key);
            $str = Language::get($tmp[0]) . Language::get($tmp[1]);
            return isset($tmp[2]) ? $str . Language::get($tmp[2]) : $str;
        } else {
            return Language::get($key);
        }
    } else {
        return null;
    }
}

/**
 * 加载完成业务方法的文件
 *
 * @param string $filename
 * @param string $file_ext
 */
function loadfunc($filename, $file_ext = '.php')
{
    if (preg_match('/^[\w\d\/_.]+$/i', $filename . $file_ext)) {
        $file = realpath(BASE_PATH . '/framework/function/' . $filename . $file_ext);
    } else {
        $file = false;
    }
    if (!$file) {
        exit($filename . $file_ext . ' isn\'t exists!');
    } else {
        require_once($file);
    }
}

/**
 * 实例化类
 *
 * @param string $model_name 模型名称
 * @return obj 对象形式的返回结果
 */
function nc_class($classname = null)
{
    static $_cache = array();
    if (!is_null($classname) && isset($_cache[$classname])) return $_cache[$classname];
    $file_name = BASE_PATH . '/framework/libraries/' . $classname . '.class.php';
    $newname = $classname . 'Class';
    if (file_exists($file_name)) {
        require_once($file_name);
        if (class_exists($newname)) {
            return $_cache[$classname] = new $newname();
        }
    }
    throw_exception('Class Error:  Class ' . $classname . ' is not exists!');
}

/**
 * 加载广告
 *
 * @param  $ap_id 广告位ID
 * @param $type 广告返回类型 html,js
 */
function loadadv($ap_id = null, $type = 'html')
{
    if (!is_numeric($ap_id)) return false;
    if (!function_exists('advshow')) import('function.adv');
    return advshow($ap_id, $type);
}

/**
 * 格式化ubb标签
 *
 * @param string $theme_content/$reply_content 话题内容/回复内容
 * @return string
 */
function ubb($ubb)
{
    $ubb = str_replace(array(
        '[B]',
        '[/B]',
        '[I]',
        '[/I]',
        '[U]',
        '[/U]',
        '[IMG]',
        '[/IMG]',
        '[/FONT]',
        '[/FONT-SIZE]',
        '[/FONT-COLOR]'
    ), array(
        '<b>',
        '</b>',
        '<i>',
        '</i>',
        '<u>',
        '</u>',
        '<img class="pic" src="',
        '"/>',
        '</span>',
        '</span>',
        '</span>'
    ), preg_replace(array(
        "/\[URL=(.*)\](.*)\[\/URL\]/iU",
        "/\[FONT=([A-Za-z ]*)\]/iU",
        "/\[FONT-SIZE=([0-9]*)\]/iU",
        "/\[FONT-COLOR=([A-Za-z0-9]*)\]/iU",
        "/\[SMILIER=([A-Za-z_]*)\/\]/iU",
        "/\[FLASH\](.*)\[\/FLASH\]/iU",
        "/\\n/i"
    ), array(
        "<a href=\"$1\" target=\"_blank\">$2</a>",
        "<span style=\"font-family:$1\">",
        "<span style=\"font-size:$1px\">",
        "<span style=\"color:#$1\">",
        "<img src=\"" . CIRCLE_SITE_URL . '/templates/' . TPL_CIRCLE_NAME . "/images/smilier/$1.png\">",
        "<embed src=\"$1\" type=\"application/x-shockwave-flash\" allowscriptaccess=\"always\" allowfullscreen=\"true\" wmode=\"opaque\" width=\"480\" height=\"400\"></embed>",
        "<br />"
    ), $ubb));
    return $ubb;
}
/**
 * 去掉ubb标签
 *
 * @param string $theme_content/$reply_content 话题内容/回复内容
 * @return string
 */
function removeUBBTag($ubb)
{
    $ubb = str_replace(array(
        '[B]',
        '[/B]',
        '[I]',
        '[/I]',
        '[U]',
        '[/U]',
        '[/FONT]',
        '[/FONT-SIZE]',
        '[/FONT-COLOR]'
    ), array(
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        ''
    ), preg_replace(array(
        "/\[URL=(.*)\](.*)\[\/URL\]/iU",
        "/\[FONT=([A-Za-z ]*)\]/iU",
        "/\[FONT-SIZE=([0-9]*)\]/iU",
        "/\[FONT-COLOR=([A-Za-z0-9]*)\]/iU",
        "/\[SMILIER=([A-Za-z_]*)\/\]/iU",
        "/\[IMG\](.*)\[\/IMG\]/iU",
        "/\[FLASH\](.*)\[\/FLASH\]/iU",
        "<img class='pi' src=\"$1\"/>",
    ), array(
        "$2",
        "",
        "",
        "",
        "",
        "",
        "",
        ""
    ), $ubb));
    return $ubb;
}

/**
 * 话题图片绝对路径
 *
 * @param $param string 文件名称
 * @return string
 */
function themeImagePath($param)
{
    return BASE_UPLOAD_PATH . '/' . ATTACH_CIRCLE . '/theme/' . $param;
}
/**
 * 话题图片url
 *
 * @param $param string
 * @return string
 */
function themeImageUrl($param)
{
    return UPLOAD_SITE_URL . '/' . ATTACH_CIRCLE . '/theme/' . $param;
}
/**
 * 圈子logo
 *
 * @param $param string 圈子id
 * @return string
 */
function circleLogo($id)
{
    if (file_exists(BASE_UPLOAD_PATH . '/' . ATTACH_CIRCLE . '/group/' . $id . '.jpg')) {
        return UPLOAD_SITE_URL . '/' . ATTACH_CIRCLE . '/group/' . $id . '.jpg';
    } else {
        return UPLOAD_SITE_URL . '/' . ATTACH_CIRCLE . '/default_group_logo.gif';
    }
}
/**
 * sns 来自
 * @param $param string $trace_from
 * @return string
 */
function snsShareFrom($sign)
{
    switch ($sign) {
        case '1':
        case '2':
            return L('sns_from') . '<a target="_black" href="' . SHOP_SITE_URL . '">' . L('sns_shop') . '</a>';
            break;
        case '3':
            return L('sns_from') . '<a target="_black" href="' . MICROSHOP_SITE_URL . '">' . L('nc_modules_microshop') . '</a>';
            break;
        case '4':
            return L('sns_from') . '<a target="_black" href="' . CMS_SITE_URL . '">CMS</a>';
            break;
        case '5':
            return L('sns_from') . '<a target="_black" href="' . CIRCLE_SITE_URL . '">' . L('nc_circle') . '</a>';
            break;
    }
}

/**
 * 输出聊天信息
 *
 * @return string
 */
function getChat($layout)
{
    if (!C('node_chat') || !file_exists(BASE_CORE_PATH . '/framework/libraries/chat.php')) return '';
    if (!class_exists('Chat')) import('libraries.chat');
    return Chat::getChatHtml($layout);
}
/**
 * 输出聊天信息
 *
 * @return string
 */
function getNewChat($layout)
{

    return Chat::getNewChatHtml($layout);
}
/**
 * 拼接动态URL，参数需要小写
 *
 * 调用示例
 *
 * 若指向网站首页，可以传空:
 * url() => 表示act和op均为index，返回当前站点网址
 *
 * url('search,'index','array('cate_id'=>2)); 实际指向 index.php?act=search&op=index&cate_id=2
 * 传递数组参数时，若act（或op）值为index,则可以省略
 * 上面示例等同于
 * url('search','',array('act'=>'search','cate_id'=>2));
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @param boolean $model 默认取当前系统配置
 * @param string $site_url 生成链接的网址，默认取当前网址
 * @return string
 */
function url($act = '', $op = '', $args = array(), $model = false, $site_url = '')
{
    //伪静态文件扩展名
    $ext = '.html';
    //入口文件名
    $file = 'index.php';
    $act = trim($act);
    $op = trim($op);
    $args = !is_array($args) ? array() : $args;
    //定义变量存放返回url
    $url_string = '';
    if (empty($act) && empty($op) && empty($args)) {
        return $site_url;
    }
    $act = !empty($act) ? $act : 'index';
    $op = !empty($op) ? $op : 'index';

    $model = $model ? URL_MODEL : $model;

    if ($model) {
        //伪静态模式
        $url_perfix = "{$act}-{$op}";
        if (!empty($args)) {
            $url_perfix .= '-';
        }
        $url_string = $url_perfix . http_build_query($args, '', '-') . $ext;
        $url_string = str_replace('=', '-', $url_string);
    } else {
        //默认路由模式
        $url_perfix = "act={$act}&op={$op}";
        if (!empty($args)) {
            $url_perfix .= '&';
        }
        $url_string = $file . '?' . $url_perfix . http_build_query($args);
    }
    //将商品、店铺、分类、品牌、文章自动生成的伪静态URL使用短URL代替
    $reg_match_from = array(
        '/^category-index\.html$/',
        '/^channel-index-id-(\d+)\.html$/',
        '/^goods-index-goods_id-(\d+)\.html$/',
        '/^show_store-index-store_id-(\d+)\.html$/',
        '/^show_store-goods_all-store_id-(\d+)-stc_id-(\d+)-key-([0-5])-order-([0-2])-curpage-(\d+)\.html$/',
        '/^document-index-code-([a-z_]+)\.html$/',
        '/^search-index-cate_id-(\d+)-b_id-([0-9_]+)-a_id-([0-9_]+)-ci-([0-9_]+)-key-([0-3])-order-([0-2])-type-([0-1])-gift-([0-1])-area_id-(\d+)-curpage-(\d+)\.html$/',
        '/^brand-list-brand-(\d+)-ci-([0-9_]+)-key-([0-3])-order-([0-2])-type-([0-1])-gift-([0-1])-area_id-(\d+)-curpage-(\d+)\.html$/',
        '/^brand-index\.html$/',
        '/^promotion-index\.html$/',
        '/^promotion-index-gc_id-(\d+)\.html$/',

        '/^show_groupbuy-index\.html$/',
        '/^show_groupbuy-groupbuy_detail-group_id-(\d+)\.html$/',

        '/^show_groupbuy-groupbuy_list-class-(\d+)-s_class-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',
        '/^show_groupbuy-groupbuy_soon-class-(\d+)-s_class-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',
        '/^show_groupbuy-groupbuy_history-class-(\d+)-s_class-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',

        '/^show_groupbuy-vr_groupbuy_list-vr_class-(\d+)-vr_s_class-(\d+)-vr_area-(\d+)-vr_mall-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',
        '/^show_groupbuy-vr_groupbuy_soon-vr_class-(\d+)-vr_s_class-(\d+)-vr_area-(\d+)-vr_mall-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',
        '/^show_groupbuy-vr_groupbuy_history-vr_class-(\d+)-vr_s_class-(\d+)-vr_area-(\d+)-vr_mall-(\d+)-groupbuy_price-(\d+)-groupbuy_order_key-(\d+)-groupbuy_order-(\d+)-curpage-(\d+)\.html$/',

        '/^pointshop-index.html$/',
        '/^pointprod-pinfo-id-(\d+).html$/',
        '/^pointvoucher-index.html$/',
        '/^pointgrade-index.html$/',
        '/^pointgrade-exppointlog-curpage-(\d+).html$/',
        '/^goods-comments_list-goods_id-(\d+)-type-([0-4])-curpage-(\d+).html$/'
    );
    $reg_match_to = array(
        'category.html',
        'channel-\\1.html',
        'item-\\1.html',
        'shop-\\1.html',
        'shop_view-\\1-\\2-\\3-\\4-\\5.html',
        'document-\\1.html',
        'cate-\\1-\\2-\\3-\\4-\\5-\\6-\\7-\\8-\\9-\\10.html',
        'brand-\\1-\\2-\\3-\\4-\\5-\\6-\\7-\\8.html',
        'brand.html',
        'promotion.html',
        'promotion-\\1.html',

        'groupbuy.html',
        'groupbuy_detail-\\1.html',

        'groupbuy_list-\\1-\\2-\\3-\\4-\\5-\\6.html',
        'groupbuy_soon-\\1-\\2-\\3-\\4-\\5-\\6.html',
        'groupbuy_history-\\1-\\2-\\3-\\4-\\5-\\6.html',

        'vr_groupbuy_list-\\1-\\2-\\3-\\4-\\5-\\6-\\7-\\8.html',
        'vr_groupbuy_soon-\\1-\\2-\\3-\\4-\\5-\\6-\\7-\\8.html',
        'vr_groupbuy_history-\\1-\\2-\\3-\\4-\\5-\\6-\\7-\\8.html',

        'integral.html',
        'integral_item-\\1.html',
        'voucher.html',
        'grade.html',
        'explog-\\1.html',
        'comments-\\1-\\2-\\3.html'
    );
    $url_string = preg_replace($reg_match_from, $reg_match_to, $url_string);
    return rtrim($site_url, '/') . '/' . $url_string;
}

/**
 * 商城会员中心使用的URL链接函数，强制使用动态传参数模式
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @param string $store_domian 店铺二级域名
 * @return string
 */
function urlShop($act = '', $op = '', $args = array(), $store_domain = '')
{
    // 开启店铺二级域名
    if (intval(C('enabled_subdomain')) == 1 && !empty($store_domain)) {
        return 'http://' . $store_domain . '.' . SUBDOMAIN_SUFFIX . '/';
    }
    if ($act == 'search' && $op == 'index' && $args['cate_id'] > 0 && empty($args['keyword'])) { //商品搜索列表页只有商品分类参数
        $id = intval($args['cate_id']);
        $channel_list  = rkcache('channel', true);
        if ($channel_list[$id] > 0) { //商品分类与频道的绑定
            $act = 'channel';
            $args = array();
            $args['id'] = $channel_list[$id];
        }
    }

    // 默认标志为不开启伪静态
    $rewrite_flag = false;

    // 如果平台开启伪静态开关，并且为伪静态模块，修改标志为开启伪静态
    $rewrite_item = array(
        'category:index',
        'channel:index',
        'goods:index',
        'goods:comments_list',
        'search:index',
        'show_store:index',
        'show_store:goods_all',
        'article:show',
        'article:article',
        'document:index',
        'brand:list',
        'brand:index',
        'promotion:index',
        'show_groupbuy:index',
        'show_groupbuy:groupbuy_detail',
        'show_groupbuy:groupbuy_list',
        'show_groupbuy:groupbuy_soon',
        'show_groupbuy:groupbuy_history',
        'show_groupbuy:vr_groupbuy_list',
        'show_groupbuy:vr_groupbuy_soon',
        'show_groupbuy:vr_groupbuy_history',
        'pointshop:index',
        'pointvoucher:index',
        'pointprod:pinfo',
        'pointprod:plist',
        'pointgrade:index',
        'pointgrade:exppointlog',
        'store_snshome:index',
    );
    if (URL_MODEL && in_array($act . ':' . $op, $rewrite_item)) {
        $rewrite_flag = true;
        $tpl_args = array();        // url参数临时数组
        switch ($act . ':' . $op) {
            case 'search:index':
                if (!empty($args['keyword'])) {
                    $rewrite_flag = false;
                    break;
                }
                $tpl_args['cate_id'] = empty($args['cate_id']) ? 0 : $args['cate_id'];
                $tpl_args['b_id'] = empty($args['b_id']) || intval($args['b_id']) == 0 ? 0 : $args['b_id'];
                $tpl_args['a_id'] = empty($args['a_id']) || intval($args['a_id']) == 0 ? 0 : $args['a_id'];
                $tpl_args['ci'] = empty($args['ci']) || intval($args['ci']) == 0 ? 0 : $args['ci'];
                $tpl_args['key'] = empty($args['key']) ? 0 : $args['key'];
                $tpl_args['order'] = empty($args['order']) ? 0 : $args['order'];
                $tpl_args['type'] = empty($args['type']) ? 0 : $args['type'];
                $tpl_args['gift'] = empty($args['gift']) ? 0 : $args['gift'];
                $tpl_args['area_id'] = empty($args['area_id']) ? 0 : $args['area_id'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;
            case 'show_store:goods_all':
                if (isset($args['inkeyword'])) {
                    $rewrite_flag = false;
                    break;
                }
                $tpl_args['store_id'] = empty($args['store_id']) ? 0 : $args['store_id'];
                $tpl_args['stc_id'] = empty($args['stc_id']) ? 0 : $args['stc_id'];
                $tpl_args['key'] = empty($args['key']) ? 0 : $args['key'];
                $tpl_args['order'] = empty($args['order']) ? 0 : $args['order'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;
            case 'brand:list':
                $tpl_args['brand'] = empty($args['brand']) ? 0 : $args['brand'];
                $tpl_args['ci'] = empty($args['ci']) || intval($args['ci']) == 0 ? 0 : $args['ci'];
                $tpl_args['key'] = empty($args['key']) ? 0 : $args['key'];
                $tpl_args['order'] = empty($args['order']) ? 0 : $args['order'];
                $tpl_args['type'] = empty($args['type']) ? 0 : $args['type'];
                $tpl_args['gift'] = empty($args['gift']) ? 0 : $args['gift'];
                $tpl_args['area_id'] = empty($args['area_id']) ? 0 : $args['area_id'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;

            case 'show_groupbuy:index':
            case 'show_groupbuy:groupbuy_detail':
                break;

            case 'show_groupbuy:groupbuy_list':
            case 'show_groupbuy:groupbuy_soon':
            case 'show_groupbuy:groupbuy_history':
                $tpl_args['class'] = empty($args['class']) ? 0 : $args['class'];
                $tpl_args['s_class'] = empty($args['s_class']) ? 0 : $args['s_class'];
                $tpl_args['groupbuy_price'] = empty($args['groupbuy_price']) ? 0 : $args['groupbuy_price'];
                $tpl_args['groupbuy_order_key'] = empty($args['groupbuy_order_key']) ? 0 : $args['groupbuy_order_key'];
                $tpl_args['groupbuy_order'] = empty($args['groupbuy_order']) ? 0 : $args['groupbuy_order'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;

            case 'show_groupbuy:vr_groupbuy_list':
            case 'show_groupbuy:vr_groupbuy_soon':
            case 'show_groupbuy:vr_groupbuy_history':
                $tpl_args['vr_class'] = empty($args['vr_class']) ? 0 : $args['vr_class'];
                $tpl_args['vr_s_class'] = empty($args['vr_s_class']) ? 0 : $args['vr_s_class'];
                $tpl_args['vr_area'] = empty($args['vr_area']) ? 0 : $args['vr_area'];
                $tpl_args['vr_mall'] = empty($args['vr_mall']) ? 0 : $args['vr_mall'];
                $tpl_args['groupbuy_price'] = empty($args['groupbuy_price']) ? 0 : $args['groupbuy_price'];
                $tpl_args['groupbuy_order_key'] = empty($args['groupbuy_order_key']) ? 0 : $args['groupbuy_order_key'];
                $tpl_args['groupbuy_order'] = empty($args['groupbuy_order']) ? 0 : $args['groupbuy_order'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;

            case 'goods:comments_list':
                $tpl_args['goods_id'] = empty($args['goods_id']) ? 0 : $args['goods_id'];
                $tpl_args['type'] = empty($args['type']) ? 0 : $args['type'];
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;

            case 'pointgrade:exppointlog':
                $tpl_args['curpage'] = empty($args['curpage']) ? 0 : $args['curpage'];
                $args = $tpl_args;
                break;
            case 'promotion:index':
                $args = empty($args['gc_id']) ? null : $args;
                break;
            default:
                break;
        }
    }

    return url($act, $op, $args, $rewrite_flag, SHOP_SITE_URL);
}

/**
 * 商城后台使用的URL链接函数，强制使用动态传参数模式
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @return string
 */
function urlAdmin($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL);
}
function urlAdminShop($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL . DS . ADMIN_MODULES_SHOP);
}
function urlAdminCms($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL . DS . ADMIN_MODULES_CMS);
}
function urlAdminMobile($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL . DS . ADMIN_MODULES_MOBILE);
}
function urlAdminCircle($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL . DS . ADMIN_MODULES_CIECLE);
}
function urlAdminDistribute($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, ADMIN_SITE_URL . DS . ADMIN_MODULES_DISTRIBUTE);
}
/**
 * CMS使用的URL链接函数，强制使用动态传参数模式
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @return string
 */
function urlCMS($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, CMS_SITE_URL);
}
/**
 * 圈子使用的URL链接函数，强制使用动态传参数模式
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @return string
 */
function urlCircle($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, CIRCLE_SITE_URL);
}
/**
 * 微商城使用的URL链接函数，强制使用动态传参数模式
 *
 * @param string $act control文件名
 * @param string $op op方法名
 * @param array $args URL其它参数
 * @return string
 */
function urlMicroshop($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, MICROSHOP_SITE_URL);
}
/**
 * 会员中心使用的URL链接函数，强制使用动态传参数模式
 * 
 * @param string $act control文件名
 * @param string $op op方法名
 * @param unknown $args URL其它参数
 * @return string
 */
function urlMember($act = '', $op = '', $args = array())
{
    // 默认标志为不开启伪静态
    $rewrite_flag = false;

    // 如果平台开启伪静态开关，并且为伪静态模块，修改标志为开启伪静态
    $rewrite_item = array(
        'article:show',
        'article:article'
    );
    if (URL_MODEL && in_array($act . ':' . $op, $rewrite_item)) {
        $rewrite_flag = true;
    }
    return url($act, $op, $args, $rewrite_flag, MEMBER_SITE_URL);
}
/**
 * 会员登录使用的URL链接函数，强制使用动态传参数模式
 * @param string $act control文件名
 * @param string $op op方法名
 * @param unknown $args URL其它参数
 * @return string
 */
function urlLogin($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, LOGIN_SITE_URL);
}
/**
 * 门店使用的URL链接函数，强制使用动态传参数模式
 * @param string $act control文件名
 * @param string $op op方法名
 * @param unknown $args URL其它参数
 * @return string
 */
function urlChain($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, CHAIN_SITE_URL);
}
/**
 * 分销使用的URL链接函数，强制使用动态传参数模式
 * @param string $act control文件名
 * @param string $op op方法名
 * @param unknown $args URL其它参数
 * @return string
 */
function urlDistribute($act = '', $op = '', $args = array())
{
    return url($act, $op, $args, false, DISTRIBUTE_SITE_URL);
}
/**
 * 验证是否为平台店铺
 *
 * @return boolean
 */
function checkPlatformStore($store_id = 0)
{
    if (isset($_SESSION['is_own_shop'])) {
        return $_SESSION['is_own_shop'];
    } else {
        $own_shop_ids = Model('store')->getOwnShopIds(true);
        return in_array($store_id, $own_shop_ids);
    }
}

/**
 * 验证是否为平台店铺 并且绑定了全部商品类目
 *
 * @return boolean
 */
function checkPlatformStoreBindingAllGoodsClass($store_id = 0, $bind_all_gc = 0)
{
    if (isset($_SESSION['is_own_shop'])) {
        return checkPlatformStore() && $_SESSION['bind_all_gc'];
    } else {
        return $store_id && $bind_all_gc;
    }
}

/**
 * 将字符部分加密并输出
 * @param string $str
 * @param int $start 从第几个位置开始加密(从1开始)
 * @param int $length 连续加密多少位
 * @return string
 */
function encryptShow($str, $start, $length)
{
    $end = $start - 1 + $length;
    $array = str_split($str);
    foreach ($array as $k => $v) {
        if ($k >= $start - 1 && $k < $end) {
            $array[$k] = '*';
        }
    }
    return implode('', $array);
}

/**
 * 加密右侧部分
 *
 * @param $str
 * @param $start
 * @return string
 */
function rightEncrypt($str, $start)
{
    $length = strlen($str);

    if ($length <= $start) {
        return $str;
    }

    return substr_replace($str, str_repeat('*', $length - $start), $start, $length - $start);
}

/**
 * 规范数据返回函数
 * @param unknown $state
 * @param unknown $msg
 * @param unknown $data
 * @return array
 */
function callback($state = true, $msg = '', $data = array())
{
    return array('state' => $state, 'msg' => $msg, 'data' => $data);
}

/**
 * flexigrid.js返回的数组
 * @param array $in_array 需要进行赋值的数据（提供给页面中JS使用）
 * @param array $fields_array 赋值下标的数组
 * @param array $data 从数据库读出的未处理数据
 * @param array $format_array 格式化价格下标的数组
 * @return array 处理后的数据
 */
function getFlexigridArray($in_array, $fields_array, $data, $format_array = array())
{
    $out_array = $in_array;
    if (empty($out_array['operation'])) {
        $out_array['operation'] = '--';
    }
    if (!empty($fields_array) && is_array($fields_array)) {
        foreach ($fields_array as $key => $value) {
            $k = '';
            if (is_int($key)) {
                $k = $value;
            } else {
                $k = $key;
            }
            if (is_array($data) && array_key_exists($k, $data)) {
                $out_array[$k] = $data[$k];
                if (!empty($format_array) && in_array($k, $format_array)) {
                    $out_array[$k] = ncPriceFormat($data[$k]);
                }
            } else {
                $out_array[$k] = '--';
            }
        }
    }
    return $out_array;
}

/**
 * flexigrid.js返回的数组列表
 * @param array $list 从数据库读出的未处理列表
 * @param array $fields_array 赋值下标的数组
 * @param array $format_array 格式化价格下标的数组
 * @return array 处理后的数据
 */
function getFlexigridList($list, $fields_array, $format_array = array())
{
    $out_list = array();
    if (!empty($list) && is_array($list)) {
        foreach ($list as $key => $value) {
            $out_list[] = getFlexigridArray(array(), $fields_array, $value, $format_array);
        }
    }
    return $out_list;
}

/**
 * 会员标签图片
 * @param unknown $img
 * @return string
 */
function getMemberTagimage($img)
{
    return UPLOAD_SITE_URL . '/' . ATTACH_PATH . '/membertag/' . ($img != '' ? $img : 'default_tag.gif');
}

/**
 * 门店图片
 * @param string $image
 * @param int $store_id
 * @param bool $chain_erp_status
 * @return string
 */
function getChainImage($image, $store_id, $chain_erp_status = false)
{
    if (empty($image)) {
        return UPLOAD_SITE_URL . DS . ATTACH_COMMON . DS . 'default_chain.jpg';
    }

    if (strpos($image, 'http') === 0) {
        return $image;
    }

    return Storage::url(ATTACH_CHAIN . DS . $store_id . DS . $image);
}

/**
 * 分销提现方式
 * @param string $payment_code
 * @return string
 */
function getDistriBillName($payment_code)
{
    return str_replace(
        array('bank', 'alipay'),
        array('银行账号', '支付宝'),
        $payment_code
    );
}

/**
 * 获取唯一码
 * @param $key
 * @return string
 */
function getUniqueCode($key)
{
    static $guid = '';
    $uid = uniqid("", true);
    $data = $key;
    $data .= $_SERVER['REQUEST_TIME'];
    $data .= $_SERVER['HTTP_USER_AGENT'];
    $data .= $_SERVER['LOCAL_ADDR'];
    $data .= $_SERVER['LOCAL_PORT'];
    $data .= $_SERVER['REMOTE_ADDR'];
    $data .= $_SERVER['REMOTE_PORT'];
    $hash = strtoupper(hash('ripemd128', $uid . $guid . md5($data)));
    $guid = substr($hash,  0,  8) .
        '-' .
        substr($hash,  8,  4) .
        '-' .
        substr($hash, 12,  4) .
        '-' .
        substr($hash, 16,  4) .
        '-' .
        substr($hash, 20, 12);
    return $guid;
}

/**
 * 向第三方发消息
 * @param string $method 方法
 * @param array $param 数组
 * @return array 处理后的数据
 */
function RealTimePush($method, $param)
{
    $logic_realtime_msg = Logic('realtime_msg');
    if (method_exists($logic_realtime_msg, $method)) {
        $logic_realtime_msg->$method($param);
    }
}

/**
 * 获取分表数据表名
 * @param string $table 表名
 * @param int $id 编号
 * @param int $split_amount 分表数
 * @return bool|string 完整表名|false
 */
function getSplitTableName($table = '', $id = 1, $split_amount = 5)
{
    $table_name = $table;
    if (trim($table_name) == '' || intval($id) <= 0) {
        $error = 'DB Error:  Data Table ' . $table . '_' . $id % $split_amount . ' is not exists!';
        throw_exception($error);
    }
    $mod = $id % $split_amount;
    if ($mod > 0) {
        $table_name .= '_' . $mod;
    }
    return $table_name;
}

/**
 * 敏感词过滤方法
 * @param string $str 需要过滤的字符串
 * @return string 过滤后的字符串
 */
function sensitiveWordFilter($str = '')
{
    $bad_word = rkcache('sensitive_word', true);
    $bad_word_new = array_combine($bad_word, array_fill(0, count($bad_word), '*'));
    $str_new = strtr($str, $bad_word_new);
    return $str_new;
}

/**
 * 敏感词检测方法
 * @param string $str 需要检测的字符串
 * @return boolean true存在，false不存在
 */
function sensitiveWordDetection($str = '')
{
    $bad_word = rkcache('sensitive_word', true);
    $m = 0;
    for ($i = 0; $i < count($bad_word); $i++) {
        if (substr_count($str, $bad_word[$i]) > 0) {
            $m++;
        }
    }
    if ($m > 0) {
        return true;
    }
    return false;
}

/**
 * 根据地址获取坐标【调用百度地图接口】
 * @param string $address 具体地址
 * @param string  $city 所在城市
 * @return  array 接口返回地址Geo信息
 */
function getGeoByAddress($address, $city = '')
{
    if (trim(C('baidu_map_key')) == '') return array();
    $url = "http://api.map.baidu.com/geocoder/v2/?address=" . urlencode($address) . "&output=json&ak=" . C('baidu_map_key') . "&city=" . urlencode($city);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    $url_result = curl_exec($ch);
    curl_close($ch);
    $url_result = json_decode($url_result, true);
    if ($url_result['status'] != 0) {
        return array();
    }
    return $url_result['result'];
}

/**
 * 百度地图
 * 根据坐标获取地址信息
 */
function geoToAddr($lat, $lng, $type = 'BD09')
{
    if (trim(C('baidu_map_key')) == '') return array();
    $url = "http://api.map.baidu.com/geocoder/v2/?location=" . $lat . "," . $lng . "&coord_type=" . $type . "&output=json&extensions_town=true&ak=" . C('baidu_map_key');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    $url_result = curl_exec($ch);
    curl_close($ch);
    $url_result = json_decode($url_result, true);
    if ($url_result['status'] != 0) {
        return array();
    }
    return $url_result['result'];
}

/**
 * 腾讯地图地址解析
 * @param $lat 纬度
 * @param $lng 经度
 * @param $type 1=默认地址解析 0=逆地址解析($lat.$lng)
 * @return array
 */
function getqqAddress($address, $type = 0, $aid = 0)
{
    if ($type == 1) {
        $url = "https://apis.map.qq.com/ws/geocoder/v1/?address=" . urlencode($address) . "&output=json&key=" .  C('tengxun_map_key');
    } else {
        $url = "https://apis.map.qq.com/ws/geocoder/v1/?location=" . $address . "&get_poi=0&key=" . C('tengxun_map_key');
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
    curl_setopt($ch, CURLOPT_HEADER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); //这个是重点。
    $url_result = curl_exec($ch);
    curl_close($ch);
    $url_result = json_decode($url_result, true);
    if ($url_result['status'] != 0) {
        log_error('getqqAddress_' . $aid, $url_result);
        return array();
    } else {
        $datas = $url_result['result'];
        return $datas;
    }
}

/**
 * ERP GET请求
 * @param string $url
 * @param rsa boolean
 * @return mixed|boolean
 */
function doCurlGetRequest($url, $param = array())
{
    if (!empty($param)) {
        $url = $url . "?" . http_build_query($param);
    }
    $handle = curl_init();
    if (stripos($url, "https://") !== FALSE) {
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($handle, CURLOPT_SSLVERSION, 1);
    }
    $header = getHeader();
    curl_setopt($handle, CURLOPT_HTTPHEADER, $header);
    curl_setopt($handle, CURLOPT_URL, $url);
    curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($handle, CURLOPT_TIMEOUT, 30);
    $sContent = curl_exec($handle);
    $aStatus = curl_getinfo($handle);
    curl_close($handle);
    //记录日志
    /*if (stripos($url, "base/Member/add") === FALSE) {
        Model('member')->addApiRequestLog($url, $header, $param, $sContent);
    }*/
    return (json_encode(array('http_code' => $aStatus['http_code'], 'msg' => $sContent)));
}
/**
 * ERP POST请求
 * @param string $url
 * @param array $param
 * @return mixed|boolean
 */
function doCurlPostRequest($url, $param, $json = false, $rsa = true)
{

    $handle = curl_init();
    if (stripos($url, "https://") !== FALSE) {
        curl_setopt($handle, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($handle, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($handle, CURLOPT_SSLVERSION, 1);
    }

    if ($rsa) {
        $header = getHeader($param);
    }

    //类型为json	
    curl_setopt($handle, CURLOPT_URL, $url);
    curl_setopt($handle, CURLOPT_RETURNTRANSFER, 1);
    if ($json) {
        $param = json_encode($param);
        array_push($header, 'Expect: ');
        array_push($header, 'Content-type:application/json');
    }

    curl_setopt($handle, CURLOPT_HTTPHEADER, $header);
    curl_setopt($handle, CURLOPT_TIMEOUT, 300);

    curl_setopt($handle, CURLOPT_POST, TRUE);
    curl_setopt($handle, CURLOPT_POSTFIELDS, $param);
    $sContent = curl_exec($handle);
    $aStatus = curl_getinfo($handle);

    $rs = array('http_code' => $aStatus['http_code'], 'msg' => $sContent);

    if (curl_errno($handle)) {
        $rs['curl_error'] = curl_error($handle);
    }

    curl_close($handle);

    return $rs;
}


/**
 * 获取客户端的IP地址
 * 
 */
function getClientIp()
{
    $realip = '';
    $unknown = '127.0.0.1';
    if (isset($_SERVER)) {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR']) && strcasecmp($_SERVER['HTTP_X_FORWARDED_FOR'], $unknown)) {
            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            foreach ($arr as $ip) {
                $ip = trim($ip);
                if ($ip != 'unknown') {
                    $realip = $ip;
                    break;
                }
            }
        } else if (isset($_SERVER['HTTP_CLIENT_IP']) && !empty($_SERVER['HTTP_CLIENT_IP']) && strcasecmp($_SERVER['HTTP_CLIENT_IP'], $unknown)) {
            $realip = $_SERVER['HTTP_CLIENT_IP'];
        } else if (isset($_SERVER['REMOTE_ADDR']) && !empty($_SERVER['REMOTE_ADDR']) && strcasecmp($_SERVER['REMOTE_ADDR'], $unknown)) {
            $realip = $_SERVER['REMOTE_ADDR'];
        } else {
            $realip = $unknown;
        }
    } else {
        if (getenv('HTTP_X_FORWARDED_FOR') && strcasecmp(getenv('HTTP_X_FORWARDED_FOR'), $unknown)) {
            $realip = getenv('HTTP_X_FORWARDED_FOR');
        } else if (getenv('HTTP_CLIENT_IP') && strcasecmp(getenv('HTTP_CLIENT_IP'), $unknown)) {
            $realip = getenv('REMOTE_ADDR');
        } else {
            $realip = $unknown;
        }
    }
    $realip = preg_match("/[\d\.]{7,15}/", $realip, $matches) ? $matches[0] : $unknown;
    return  $realip;
}

/**
 * 根据客户端IP地址获取城市名称[调用百度地图接口、提供备用方案调用淘宝接口]
 * @param int $ip 客户端的IP地址
 * 
 */
function getCityNameByIp($ip = '')
{
    if (empty($ip)) {
        $ip = getClientIp();
    }
    // 使用百度的API来进行调用
    $res = @file_get_contents('http://api.map.baidu.com/location/ip?ak=2TGbi6zzFm5rjYKqPPomh9GBwcgLW5sS&ip=' . $ip . '&coor=bd09ll');
    if (empty($res)) {
        return false;
    } else {
        // 使用淘宝的API来进行调用
        $requestUrl = 'http://ip.taobao.com/service/getIpInfo.php?ip=' . $ip;
        $content = file_get_contents($requestUrl);
        $result  = json_decode($content, TRUE);
        if ($result['code'] == 1) {
            return false;
        } else {
            return $result['data']['city'];
        }
    }
    $json = json_decode($res, true);
    $data['address'] = $json['content']['address_detail']['city'];
    return $data['address'];
}

/**
 * 获取自定义的header数据
 * @param string $sign 签名字符串
 * @return array
 */
function getHeader($param = array())
{

    $headers = array();
    $headers['timestamp'] = time();
    $headers['sn'] = str_pad(substr(time(), 6, 4), 9, 0, STR_PAD_LEFT) . rand(1, 1000) . rand(1, 1000);
    $headers['ua'] = '10';
    $headers['source'] = '1';

    define('SCRIPT_ROOT',  BASE_DATA_PATH . '/api/ERP');
    require_once SCRIPT_ROOT . '/rsa.php';
    $rsa = Rsa::create();

    $data = $rsa->privateSignature(array_merge($param, $headers));

    if (!empty($data)) {
        $headers['sign'] = $data;
    }

    //排序成header格式
    $header = array();
    foreach ($headers as $k => $v) {
        $header[] = $k . ':' . $v;
    }
    //获取reids ERP当前登录token
    $erp_login = $_SESSION['jsession'];

    if ($erp_login) {
        $header[] = 'Authorization: Bearer ' . $erp_login['jwt']; //$_SESSION['jsession']['data']
    }


    return $header;
}

/**
 * 获取聊天图片消息
 * @param string $path
 * @return string|void
 */
function getChatImage($path = '')
{
    if (empty($path)) {
        return;
    }

    return Storage::url(ATTACH_CHAT . DS . $path);
}

function fun_curl($url, $data)
{
    $curl = new Curl();
    $curl->setDefaultJsonDecoder(true);
    $curl->setHeaders([
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
    ]);

    return $curl->post(
        $url,
        json_encode($data, JSON_UNESCAPED_UNICODE)
    );
}

/*
 *
 * */
function apiRequest($url = '', $params = array(), $request_type = "get", $header = array())
{
    require_once BASE_DATA_PATH . "/api/baiduapi/lib/AipHttpClient.php";

    $api = new AipHttpClient();
    try {
        if ($request_type == "get") {
            $result = $api->$request_type($url, $params, $header);
        } else {
            $result = $api->$request_type($url, $params, array(), $header);
        }
        if ($result['code'] == 200) {
            $content = json_decode($result['content'], true);
            return $content;
        } else {
            log_error("apiRequest", json_decode($result['content'], true));
            return false;
        }
    } catch (Exception $e) {
        log_error("apiRequest", ['error' => $e->getMessage()]);
        //接口异常
        return false;
    }
}

/**
 * PHP发送Json对象数据 拆单用
 *
 * @param $url 请求url
 * @param $jsonStr 发送的json字符串
 * @return array
 */
function http_post_json($url, $jsonStr, $phone = "", $order_sn = 0, $type = 0, $channel_id = '5', $user_agent = 3)
{
    //微信广告头部标识
    //渠道来源,1-Android,2-iOS,3-小程序,4-公众号,5-Web,6-其它，7-竖屏
    $click_id = $_SERVER['HTTP_CLICK_ID'] ?: '';
    if ($_POST['source'] == 9 || $user_agent == 9) {
        $user_agent = 1;
    } elseif ($_POST['source'] == 10 || $user_agent == 10) {
        $user_agent = 2;
    }
    $header = array(
        'Content-Type: application/json; charset=utf-8',
        'Content-Length: ' . strlen($jsonStr),
        'channel_id: ' . $channel_id,
        'user_agent: ' . $user_agent,
        'click_id: ' . $click_id,
    );

    if ($phone) {
        // 兼容手机号及加密手机号
        if (!is_numeric($phone)) {
            $phone = rc4(base64_decode($phone));
        }
        $token = isset($_REQUEST['key']) ? $_REQUEST['key'] : getTokenByPhone($phone);
        $header[] = 'Authorization: Bearer ' . $token;
        $m_info = Model('member')->getMemberInfo(['member_mobile' => $phone], "weixin_mini_openid", false);
        if ($m_info['weixin_mini_openid']) {
            $header[] = 'open_id: ' . $m_info['weixin_mini_openid'];
        }
    }
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    Model('member')->addApiRequestLog($url, json_encode($header), $jsonStr, $response, $order_sn, $type);
    return array($httpCode, $response);
}
function ppp($data, $type = 1)
{
    if ($type > 1) {
        echo '<pre>';
        print_r($data);
        die;
    } else {
        echo '<pre>';
        print_r($data);
    }
}
function runTimeLog($start_time, $end_time, $name = "")
{
    $time = $name . '共' . round($end_time - $start_time, 3) . '秒';
    error_log(print_r($time . PHP_EOL, true), 3, BASE_ROOT_PATH . '/run_time_log.log');
    return $time;
}

function public_excel($excel_data, $export_name)
{
    Language::read('export');
    import('libraries.excel');
    $excel_obj = new Excel();
    //设置样式
    $excel_obj->setStyle(array('id' => 's_title', 'Font' => array('FontName' => '宋体', 'Size' => '12', 'Bold' => '1')));
    $excel_data = $excel_obj->charset($excel_data, CHARSET);
    $excel_obj->addArray($excel_data);
    $excel_obj->addWorksheet($excel_obj->charset($export_name, CHARSET));
    $excel_obj->generateXML($excel_obj->charset($export_name, CHARSET) . '-' . date('Y-m-d-H', time()));
}

/**
 * 接口请求异常记录/任务计划执行
 * @param int $order_id
 * @param array $info
 * @param string $msg
 * @param string $logname
 */
function redis_error_log($order_id, $info, $msg, $log_name)
{
    //异常订单处理
    $arr = [];
    $arr["order_id"] = $order_id;
    $arr["info"] = $info;
    $arr['msg'] = $msg;
    $arr['date_time'] = date("Y-m-d H:i:s", time());
    $push = rkcache($log_name);
    if (!$push) {
        $push = [];
    }
    $push[$order_id] = $arr;
    wkcache($log_name, $push);
}
/**
 * 获取阿闻微页面数据
 * @param $path
 * @param $data
 * @return array|mixed
 */
function getAwasqContent($path, $data)
{
    $url = C('aw_request_url') . $path;
    $time = time();
    $sign = 'AppId=' . C('aw_appid') . "&Secret=" . C('aw_secret') . "&Url=" . $path . "&Timestamp=" . $time . "&Version=" . C('aw_version');
    $sign = strtoupper(md5($sign));
    $data = json_encode($data);
    $header = array();
    $header[] = 'Content-Type:application/json;';
    $header[] = 'Cache-Control: no-cache';
    $header[] = 'focus-auth-appid: ' . C('aw_appid');
    $header[] = 'focus-auth-version: ' . C('aw_version');
    $header[] = 'focus-auth-timestamp: ' . $time;
    $header[] = 'focus-auth-sign: ' . $sign;
    $header[] = 'focus-auth-url: ' . $path;
    $header[] = 'focus-auth-userid: 0';
    $header[] = 'focus-auth-username: 0';
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1);
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    curl_setopt($curl, CURLOPT_TIMEOUT, 60);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    $result = curl_exec($curl);
    if (curl_errno($curl)) {
        return ['state' => false, 'msg' => curl_error($curl)];
    }
    curl_close($curl);

    $arr_data = json_decode($result, true);
    return $arr_data;
}

/**
 * 递归创建目录
 * @param $dir
 * @return bool
 */
//if (!function_exists('Directory')){
//    function Directory( $dir ){
//        return  is_dir ( $dir ) or Directory(dirname( $dir )) and  mkdir ( $dir , 0777);
//    }
//}

/**
 * 打印函数
 * @param $data
 */
function pre($data)
{
    echo "<pre>" . print_r($data, true) . "</pre>";
}

/**
 * redis list 日志记录
 *
 * @param string $messge 错误信息
 * @param mixed $data 附加数据，可以是数组、字符串或其他类型，可选参数
 */
function log_error($messge, $data = [])
{
    $path = BASE_ROOT_PATH . DS . 'data' . DS . 'log' . DS;
    if (!is_dir($path)) {
        mkdir(iconv("UTF-8", "GBK", $path), 0777, true);
    }

    $datas = [
        'time' => date('Y-m-d H:i:s'),
        'error' => $messge,
        'data' => $data,
    ];
    error_log(PHP_EOL . json_encode($datas, JSON_UNESCAPED_UNICODE), 3, $path . date('Ymd') . '.err.log');
}

/**
 * 信息日志记录
 *
 * @param string $messge 信息内容
 * @param mixed $data 附加数据，可以是数组、字符串或其他类型，可选参数
 */
function log_info($messge, $data = [])
{
    $path = BASE_ROOT_PATH . DS . 'data' . DS . 'log' . DS;
    if (!is_dir($path)) {
        mkdir(iconv("UTF-8", "GBK", $path), 0777, true);
    }

    $datas = [
        'time' => date('Y-m-d H:i:s'),
        'info' => $messge,
        'data' => $data,
    ];
    error_log(PHP_EOL . json_encode($datas, JSON_UNESCAPED_UNICODE), 3, $path . date('Ymd') . '.run.log');
}

/**
 * 弱密码校验
 * @param $password
 * @return bool
 */
function isWeakPassword($password)
{
    return !preg_match("/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:;'<>,.]).{8,20}/", $password);
}

/**
 * 登录限制，30分钟内
 * @param $key
 * @param $type 默认计数，get-返回计数结果，del-清除计数器
 * @return false|mixed|string
 */
function loginLimit($key, $type = '')
{
    switch ($type) {
        case 'get':
            return Redis::get($key);
        case 'del':
            Redis::expire($key, -1);
            return true;
        default:
            Redis::incr($key);
            Redis::expire($key, 30 * 60);
    }
    return true;
}

/**
 * rsa解密
 * @param $str
 * @return string
 */
function rsaDecrypt($str)
{
    //rsa 私钥
    $private_key = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    //私钥解密
    openssl_private_decrypt(base64_decode($str), $decrypted, $private_key);
    if (!empty($decrypted)) {
        return $decrypted;
    }
    return '';
}

/**
 * Notes:公钥加密
 * @param $str
 * @return string
 * User: rocky
 */
function rsaEncrypt($str)
{
    $pu_key = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDlOJu6TyygqxfWT7eLtGDwajtN
FOb9I5XRb6khyfD1Yt3YiCgQWMNW649887VGJiGr/L5i2osbl8C9+WJTeucF+S76
xFxdU6jE0NQ+Z+zEdhUTooNRaY5nZiu5PgDB0ED/ZKBUSLKL7eibMxZtMlUDHjm4
gwQco1KRMDSmXSMkDwIDAQAB
-----END PUBLIC KEY-----';
    openssl_public_encrypt($str, $encrypted, $pu_key);
    $encrypted = base64_encode($encrypted);
    if (!empty($encrypted)) {
        return $encrypted;
    }
    return '';
}

/**
 * 字符串隐藏，用于手机号，邮箱，身份证等隐私信息
 * @param $str
 * @param int $type 1-手机号，2-邮箱，3-身份证
 * @return mixed
 */
function hideStr($str, $type = 1)
{
    $len = strlen($str);
    if ($len < 2) return $str;
    switch ($type) {
        case 1:
            $hide_str = str_repeat('*', 4);
            $str =  substr_replace($str, $hide_str, 3, -4);
            break;
        case 2:
            $prefix = substr($str, 0, strpos($str, '@'));
            $prefix = substr($prefix, 0, 2) . str_repeat('*', strlen($prefix) >= 2 ? (strlen($prefix) - 2) : 0);
            $str = $prefix . substr($str, strlen($prefix));
        case 3:
            $hide_str = str_repeat('*', 10);
            $str = substr_replace($str, $hide_str, 4, -4);
        case 4:
            $str = preg_replace('/(1[3456789]{1}[0-9])[0-9]{4}([0-9]{4})/i', '$1****$2', $str);
            break;
        default:
            break;
    }
    return $str;
}

/**
 * Notes:通过手机号获取token
 * @param $phone
 * @return mixed
 * @throws Exception
 * User: rocky
 * DateTime: 2021/9/15 18:37
 */
function getTokenByPhone($phone)
{
    $key = 'token:' . $phone;
    if (rkcache($key)) {
        $token = rkcache($key);
    } else {
        $member = Model('member')->getMemberInfo(['member_mobile' => $phone], "member_id,scrm_user_id,weixin_mini_openid,member_mobile");
        if (empty($member)) {
            throw new Exception('获取token无用户信息');
        }
        $connect_api_logic = Logic('connect_api');
        $token = $connect_api_logic->getJwtToken(['member_id' => (int)$member['member_id'], 'scrmid' => $member['scrm_user_id'], 'openid' => $member['weixin_mini_openid'], 'mobile' => $member['member_mobile'], 'phone' => $member['member_mobile']]);
        wkcache($key, $token, 3600);
    }
    if ($token) {
        return $token;
    }
    Redis::lPush('erp_gettoken_log', json_encode(['time' => date('Y-m-d H:i:s'), 'param' => $phone, 'result' => $token, 'member' => $member], JSON_UNESCAPED_UNICODE));
    throw new Exception('获取用户token失败');
}

/**
 * Notes:订单来源
 * @param $source
 * @return int
 * User: rocky
 * DateTime: 2021/9/26 18:03
 */
function order_source($source)
{
    $user_agent = 3;
    if ($source == 9) {
        $user_agent = 1;
    } elseif ($source == 10) {
        $user_agent = 2;
    } elseif ($source == 1 || $source == 4) {
        $user_agent = 7;
    }
    return $user_agent;
}

/**
 * Notes:口令更新周期（3个月）
 * User: rocky
 * DateTime: 2021/9/28 10:54
 */
function setPasswordCycle($key, $type = 0)
{
    if ($type) {
        Redis::set('password_admin_' . $key, $key, 3600 * 24 * 90);
    } else {
        Redis::set('password_seller_' . $key, $key, 3600 * 24 * 90);
    }
}

/**
 * Notes:企业微信扫码登录回调
 * https://work.weixin.qq.com/api/doc/90000/90135/91437
 * User: rocky
 * DateTime: 2021/11/3 16:32
 */
function weixinLogin($code)
{
    try {
        $key = 'wx_access_token';
        if (rkcache($key)) {
            $access_token = rkcache($key);
        } else {
            $appid = C('work_weixin_appid');
            $secret = C('work_weixin_secret');
            $url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=' . $appid . '&corpsecret=' . $secret;
            $token = https_request($url);
            $access_token = $token['access_token'];
            wkcache($key, $access_token, $token['expires_in']);
        }
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=' . $access_token . '&code=' . $code;
        $usersinfo = https_request($url);
        if (isset($usersinfo['UserId'])) {
            $url = 'https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=' . $access_token . '&userid=' . $usersinfo['UserId'];
            $userinfo = https_request($url);
            if ($userinfo['errcode'] == 0) {
                return ['state' => true, 'data' => $userinfo];
            } else {
                throw new Exception('该用户无权限登录！');
            }
        } else {
            throw new Exception(json_encode($usersinfo));
        }
    } catch (Exception $ex) {
        return ['state' => false, 'msg' => $ex->getMessage()];
    }
}



/**
 * curl
 */
function https_request($url)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $data = json_decode(curl_exec($curl), true);
    // $data=curl_exec($curl);
    curl_close($curl);
    return $data;
}

/**
 * Notes:校验登录加密签名
 * @param $sign
 * @return mixed|string
 * User: rocky
 * DateTime: 2021/11/8 11:46
 */
function checkSign($http_token)
{
    try {
        action(new \Upet\Modules\Member\Actions\VerifyJwtSignAction($http_token));
    } catch (Exception $e) {
        return ['state' => false, 'msg' => $e->getMessage()];
    }
    $exp = explode('.', $http_token);
    $body = json_decode(base64_decode($exp[1]), true);
    return  ['state' => true, 'mobile' => $body['mobile']];
}

// 此功能未开启且已废弃
if (!function_exists('get_magic_quotes_gpc')) {
    function get_magic_quotes_gpc()
    {
        return 0;
    }
}

/**
 * Notes:获取统一认证中心auth-center的access_token
 * @return {"code": 200,"message": "","data": access_token}
 * User: rocky
 * DateTime: 2021/12/8 9:07
 */
function getWechatAuthSign($store_id)
{
    $url = C('aw_wechat_url') . '/auth-center/wechat/get-access-token';
    $time = time();
    $sign = 'appId' . C('aw_wechat_appid') . "timestamp" . $time . C('aw_wechat_secretcode');
    $sign = md5($sign);
    $header = array();
    $header[] = 'Content-Type:application/json;';
    $header[] = 'Cache-Control: no-cache';
    $header[] = 'appId: ' . C('aw_wechat_appid');
    $header[] = 'timestamp: ' . $time;
    $header[] = 'channel: ' . C('aw_wechat_channel_' . $store_id);
    $header[] = 'sign: ' . $sign;
    $header[] = 'focus-auth-url: ' . $url;
    $header[] = 'focus-auth-userid: 0';
    $header[] = 'focus-auth-username: 0';
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1);
    curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $header);

    $result = curl_exec($curl);
    if (curl_errno($curl)) {
        return ['state' => false, 'msg' => curl_error($curl)];
    }
    curl_close($curl);

    $arr_data = json_decode($result, true);
    return $arr_data;
}

/**
 * 互联网医院open api签名
 * http://nas.mayb.cn:5000/ns/sharing/dc8GB
 * signing_string 的拼接规则为：signing_string = HTTP Method + \n + HTTP URI + \n + canonical_query_string + \n + access_key + \n\n
 * @param array $params 请求参数, json字符串或form数组参数
 * @param string $uri  "/grpc-gin-empty/test"
 * @param string $method "GET POST"
 * @return array "['sign'=>'', 'sign_body'=>'']"
 */
function signForHospital($uri, $params, $method = 'POST')
{
    $data = array('sign' => '', 'sign_body' => '');
    $access_key = C('HOSPITAL_ACCESS_KEY');
    $secret_key = C('HOSPITAL_SECRET_KEY');

    $sign_str = strtoupper($method) . PHP_EOL . $uri . PHP_EOL;
    // A、确定参数类型，json字符串或数组，数组按照字典顺序（ ASCII 码由小到大）排序，多个使用 & 符号连接起来，示例：a=1&b=2
    if (is_array($params)) {
        if (count($params) > 0) {
            ksort($params);
            foreach ($params as $k => $v) {
                $sign_str .= ($k . '=' . $v . '&');
            }
            $sign_str = rtrim($sign_str, '&');
        }
    }
    $sign_str .= PHP_EOL;

    // B、拼接 access_key
    $sign_str .= $access_key . PHP_EOL . PHP_EOL;

    // C、签名计算
    $data['sign'] = base64_encode(hash_hmac('sha256', $sign_str, $secret_key, true));
    $data['sign_body'] = base64_encode(hash_hmac('sha256', is_string($params) ? $params : '', $secret_key, true));

    return $data;
}

/**
 * post - json请求
 * @param $url
 * @param $jsonStr
 * @param string[] $header
 * @return array
 */
function httpPostJson($url, $jsonStr, $header = array('Content-Type: application/json; charset=utf-8'), $timeout = 60)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return array($httpCode, $response);
}

// 获取毫秒数时间戳
function getMsec()
{
    list($msec, $sec) = explode(' ', microtime());
    return intval(((float)$msec + (float)$sec) * 1000);
}

// 是视频号下单
function is_wx_live($order)
{
    return $order['is_live'] == 2 && $order['payment_code'] == 'wx_jsapi';
}

// 订单来源值转换
function orderFromName($order_from, $is_virtual = 0)
{
    if ($is_virtual) {
        $data = array(
            1 => 'PC端',
            2 => '移动端',
            3 => 'APP端',
            4 => '智慧门店',
            5 => '微信小程序',
            6 => '阿闻商城小程序',
            11 => '支付宝小程序',
            18 => '百度小程序',
            99 => '视频号',
        );
    } else {
        $data = array(
            1 => 'PC端',
            2 => '移动端',
            3 => 'APP端',
            4 => 'ERP',
            5 => '智慧门店',
            6 => '有赞',
            7 => '微信小程序',
            8 => '阿闻商城小程序',
            11 => '支付宝小程序',
            18 => '百度小程序',
            99 => '视频号',
        );
    }
    return isset($data[$order_from]) ? $data[$order_from] : '';
}

/**
 * Notes:订单来源
 * @param array $param
 * @return array
 * User: rocky
 * DateTime: 2023/11/10 10:40
 */
function getOrderSource($source)
{
    switch ($source) {
        case 1:
            $order_from = 5; //小程序(阿闻智慧门店)
            break;
        case 2:
            $order_from = 7; //阿闻宠物(北京那边用)
            break;
        case 3:
            $order_from = 8; //阿闻商城
            break;
        case 9:
            $order_from = 3; //安卓
            break;
        case 10:
            $order_from = 3; //ios
            break;
        case 11:
            $order_from = 11; //支付宝小程序
            break;
        case 18:
            $order_from = 18; //百度小程序
            break;
        default:
            $order_from = 2;
            break;
    }
    return $order_from;
}

/**
 * Notes:查看手机号加密信息记录
 * @param order_id 订单号id
 * @type 1:实物接收手机号 2：虚拟订单接收手机号 3：实物用户手机号
 * $platfrom 0 商家中心 1电商平台 2门店管理
 * @return array
 * User: rocky
 * DateTime: 2022/9/19 18:53
 */
function showphone($type, $order_id, $operator)
{
    $condition['order_id'] = $order_id;
    $text = '查看手机号';
    $ciphertext = '';
    switch ($type) {
        case 1:
            $orderCommon = OrderCommon::where($condition)->field('reciver_info')->find();
            $reciver_info = unserialize($orderCommon['reciver_info']);
            $ciphertext = $reciver_info['encrypt_mobile'];
            $phone = isset($reciver_info['encrypt_mobile']) ? rc4(base64_decode($reciver_info['encrypt_mobile'])) : '';
            $text = '实物订单收货人手机号';
            break;
        case 2:
            $order_info = VrOrder::where($condition)->field('encrypt_mobile')->find();
            $ciphertext = $order_info['encrypt_mobile'];
            $phone = isset($order_info['encrypt_mobile']) ? rc4(base64_decode($order_info['encrypt_mobile'])) : '';
            $text = '虚拟订单接收手机号';
            break;
        case 3:
            $member_info = Order::alias('o')->field('o.encrypt_mobile,m.member_mobile')->join('member m', 'm.member_id = o.buyer_id')->where($condition)->find();
            $ciphertext = $member_info['encrypt_mobile'];
            $phone = $member_info['member_mobile'];
            $text = '实物订单用户手机号';
        default:
            break;
    }
    if (empty($phone)) {
        return ['status' => 400, 'msg' => '查询不到记录'];
    }
    if ($_POST['text'] == '隐藏') {
        $phone = hideStr($phone);
    }
    // 日志记录
    if ($_POST['text'] == '显示') {
        \Upet\Models\Datacenter\DecryptHistory::create([
            'ciphertext' => $ciphertext,
            'app_id' => 6,
            'operator' => $operator,
            'operation_time' => date('Y-m-d H:i:s', time()),
            'ip' => getIp()
        ]);
    }
    return ['status' => 200, 'phone' => $phone, 'text' => $text];
}

/**
 * Notes:
 * @param $content 文本内容
 * @param $type 类型 1:文本  2:图片  3:音频  4:视频
 * @return array
 * User: rocky
 * DateTime: 2022/9/28 17:19
 */
function security_content($content, $type = 1)
{
    if (empty($content)) {
        return ['state' => true];
    }
    $send_data = [
        'type' => $type,
        'content' => $content
    ];
    $apiURL = C('zhiyue_url') . '/edu/external/digitization/main-moderation';
    $result = fun_curl($apiURL, $send_data);
    $state = false;
    $msg = '内容包含敏感词汇，请重新输入';
    if ($result['code'] == 200) {
        switch ($result['details']['suggestion']) {
            case 'Block':
                $msg = '内容包含敏感词汇,建议屏蔽';
                break;
            case 'Review ':
                $msg = '内容包含敏感词汇,建议复审';
                break;
            case 'Pass':
                $msg = '内容建议通过';
                $state = true;
                break;
            default:
                break;
        }
        return ['state' => $state, 'msg' => $msg];
    } else {
        log_error('内容审核失败', $result);
        return ['state' => $state, 'msg' => '请求失败'];
    }
}

/*
* rc4加密解密算法
* $rc4_key 密钥
* $data 要加密的数据
*/
function rc4($data)
{
    $key[] = '';
    $box[] = '';
    if (Redis::get('rc4_key')) {
        $rc4_key = Redis::get('rc4_key');
    } else {
        $url = C('dianyin_request_url') . '/data-security/get-key';
        //        $url = 'http://10.11.8.29:8140/data-security/get-key';
        $params['app-id'] = 10;
        $params['user-id'] = "1";
        $params['timestamp'] = time();
        $params['sign'] = mobileSecuritySign($params);
        $res = fun_curl($url, $params);
        if (is_string($res)) {
            throw new Exception($res);
        }
        $rc4_key = $res['key'];
        if (!$rc4_key) {
            throw new Exception("获取手机号密钥失败");
        }
        Redis::set('rc4_key', $rc4_key);
    }
    $pwd_length = strlen($rc4_key);
    $data_length = strlen($data);
    for ($i = 0; $i < 256; $i++) {
        $key[$i] = ord($rc4_key[$i % $pwd_length]);
        $box[$i] = $i;
    }
    for ($j = $i = 0; $i < 256; $i++) {
        $j = ($j + $box[$i] + $key[$i]) % 256;
        $tmp = $box[$i];
        $box[$i] = $box[$j];
        $box[$j] = $tmp;
    }
    for ($a = $j = $i = 0; $i < $data_length; $i++) {
        $a = ($a + 1) % 256;
        $j = ($j + $box[$a]) % 256;
        $tmp = $box[$a];
        $box[$a] = $box[$j];
        $box[$j] = $tmp;
        $k = $box[(($box[$a] + $box[$j]) % 256)];
        $cipher .= chr(ord($data[$i]) ^ $k);
    }
    return $cipher;
}

/**
 * 手机号中间4位加星
 *
 * @param $mobile string
 * @return string
 */
function mobile_star($mobile)
{
    return substr($mobile, 0, 3) . '****' . substr($mobile, 7);
}

/**
 * Notes:判断是否有绑定客服
 * @param $member_info 用户信息
 * @param $type 1=默认解绑 2=清退
 * User: rocky
 * DateTime: 2023/1/10 16:03
 */
function verifyBindService($member_info, $type = 1)
{
    $condition['member_id'] = $member_info['member_id'];
    $condition['chain_member_mobile'] = $member_info['member_mobile'];
    $condition['_op'] = 'or';
    $model_chain_bind = Model('chain_bind');
    $chain_list = $model_chain_bind->where($condition)->select();
    $data = [];
    if ($chain_list) {
        foreach ($chain_list as $v) {
            $param = [
                'member_id' => $v['member_id'],
                'chain_id' => $v['chain_id'],
                'chain_member_mobile' => $v['chain_member_mobile'],
                'binding_time' => $v['binding_time'],
                'undinding_time' => date('Y-m-d H:i:s', time()),
                'message' => $type == 1 ? $member_info['member_mobile'] . '解绑' : $member_info['member_mobile'] . '清退'
            ];
            $data[] = $param;
        }
        if ($data) {
            //删除绑定记录
            $model_chain_bind->where($condition)->delete();
            //添加日志
            Model('chain_bind_log')->insertAll($data);
        }
    }
}

function mobileSecuritySign($params)
{
    $secret_key = C('MOBILE_SECRET_KEY');
    // A、确定参数类型，json字符串或数组，数组按照字典顺序（ ASCII 码由小到大）排序，多个使用 & 符号连接起来，示例：a=1&b=2
    if (is_array($params)) {
        if (count($params) > 0) {
            ksort($params);
            foreach ($params as $k => $v) {
                $sign_str .= ($k . $v);
            }
            $sign_str = rtrim($sign_str);
        }
    }
    // B、拼接 access_key
    $sign_str .= $secret_key;
    // C、签名计算
    //    $sign = strToHex(md5($sign_str));
    $hex = md5($sign_str);
    $bin = hex2bin($hex);
    $sign = bin2hex($bin);
    return $sign;
}

function strToHex($str)
{
    $hex = '';
    for ($i = 0; $i < strlen($str); $i++) $hex .= dechex(ord($str[$i]));
    $hex = strtoupper($hex);
    return $hex;
}

/**
 * Notes:发送阿里云短信
 * @desc 支持2条/分钟，10条/小时 ，累计20条/天
 * @param $mobile 手机号
 * @param $expire_time 过期时间
 * @return bool|stdClass
 * User: rocky
 * DateTime: 2023/4/21 9:03
 */
function sendSms($mobile, $expire_time = 60 * 20)
{
    $res['code'] = 400;
    $res['msg'] = '验证码发送失败';
    // 生成6位随机数字验证码
    $code = rand(100000, 999999);
    // 限频，判断该手机号在1分钟内是否请求超过3次
    $mobile_key = 'sms_count:' . $mobile;
    $count = Redis::get($mobile_key);
    if ($count >= 3) {
        // 超过3次，发送失败
        $res['msg'] = '验证太频繁，稍后再试';
        return $res;
    }

    $params = array();
    $accessKeyId = C('SmsAccessKeyId');
    $accessKeySecret = C('SmsAccessKeySecret');;
    $params["PhoneNumbers"] = $mobile;
    $params["SignName"] = "阿闻宠物";
    $params["TemplateCode"] = C('SmsTemplateCode');
    $params['TemplateParam'] = array(
        "code" => $code,
    );
    if (!empty($params["TemplateParam"]) && is_array($params["TemplateParam"])) {
        $params["TemplateParam"] = json_encode($params["TemplateParam"], JSON_UNESCAPED_UNICODE);
    }
    $helper = new signaturehelper();
    $result = $helper->request(
        $accessKeyId,
        $accessKeySecret,
        "dysmsapi.aliyuncs.com",
        array_merge($params, array(
            "RegionId" => "cn-hangzhou",
            "Action" => "SendSms",
            "Version" => "2017-05-25",
        ))
    );
    if ($result && $result['Code'] == 'OK') {
        // 发送成功，记录该手机号的请求次数
        Redis::set('RpMillions:SmsVerifyCode:' . $mobile, $code, $expire_time);
        Redis::incr($mobile_key);
        Redis::expire($mobile_key, 60);
        $res['code'] = 200;
        $res['msg'] = '发送成功';
    } else {
        \Shopnc\log::record('获取验证码：' . hideStr($mobile) . ',' . json_encode($result, JSON_UNESCAPED_UNICODE), 'ERR');
        $res['msg'] = isset($result) ? '请求验证太频繁，稍后再试' : '短信发送异常';
    }

    return $res;
}

/**
 * Notes:短信验证码校验
 * User: rocky
 * DateTime: 2023/4/21 14:27
 */
function checkSmsCode()
{
    $phone = $_POST['phone'];
    $code = $_POST['code'];
    if (!$code) {
        output_error('请输入验证码');
    }
    if ($code == '888888') {
        return ['msg' => '验证成功'];
    }
    basicFormValidate($phone);
    $redis_code = Redis::get('RpMillions:SmsVerifyCode:' . $phone);
    if ($code != $redis_code) {
        output_error('验证码不正确');
    }
    return ['msg' => '验证成功'];
}

/**
 * Notes:手机号码校验
 * @param $phone
 * User: rocky
 * DateTime: 2023/4/21 14:26
 */
function basicFormValidate($phone)
{
    if (!$phone) {
        output_error("请填写手机号");
    }
    if (!preg_match('/^0?(13|15|17|18|14|19|16)[0-9]{9}$/i', $phone)) {
        output_error('手机号码格式错误');
    }
}

// 移除图片水印
function urlRemoveWaterMark($url)
{
    $images = [];
    foreach (explode(',http', $url) as $key => $i) {
        if ($key == 0) {
            $images[] = explode('?', $i)[0];
        } else {
            $images[] = 'http' . explode('?', $i)[0];
        }
    }
    return implode(',', $images);
}

/**
 * Notes:实现不同的店铺主体id返回支付对应的app_id，然后再根据app_id返回对应的store_id
 * Notes:主要用于支付请求时的app_id
 * @param $shop_id
 * @param $app_id 2-极宠家 3-福码购
 * @return int
 */
function getShopStoreId($app_id)
{
    $store_id = 1;
    if ($app_id == 7) {
        $store_id = 2;
    } elseif ($app_id == 8 or $app_id == 3) { //原宠商云阿闻支付用的8，经过云订单中转，则请求支付变为3（同云订货支付的app_id）
        $store_id = 3;
    }
    return $store_id;
}

/**
 * @param $shop_id 7-极宠家 8-福码购
 * @return int
 */
function getShopAppId($shop_id)
{
    $app_id = 1;
    if ($shop_id == 2) {
        $app_id = 7;
    } elseif ($shop_id == 3) {
        $app_id = 8;
    }
    return $app_id;
}

/**
 * Notes:获取店铺主体store_id对应的小程序miniappId
 * @param $store_id 2-极宠家 3-福码购
 * @return int
 */
function getShopMiniAppId($store_id, $miniappId)
{
    if ($store_id == 2) {
        $miniappId = C('jcj_wxpay_appid');
    } elseif ($store_id == 8) {
        $miniappId = C('fmg_wxpay_appid');
    }
    return $miniappId;
}

function Generate(): string
{
    $preKey = "order-center:order-sn-number";
    $exists = Redis::exists($preKey);
    if (!$exists) {
        $maxOrderSn = \Upet\Models\OrderMain::table('dc_order.order_main')->field('max(order_sn) as max_order_sn')->find();
        Redis::setnx($preKey, (int)$maxOrderSn['max_order_sn']);
    }

    $random = rand(100, 899);
    //递增$preKey的随机值，保证订单号唯一
    $orderSn = Redis::incrBy($preKey, $random);

    if (!$orderSn) {
        echo "Error incrementing Redis order number counter: " . Redis::getLastError();
        exit;
    }

    return (string) $orderSn;
}

/**
 * 重试机制，如果请求失败，延迟1秒重试
 * @param callable $operation
 * @param $errorMessage
 * @param $retries
 * @param $delay
 * @return void
 * @throws Exception
 */
function RetryOperation(callable $operation, $errorMessage, $retries = 3, $delay = 1000)
{
    for ($attempt = 1; $attempt <= $retries; $attempt++) {
        try {
            $operation();
            return;
        } catch (\Exception $e) {
            log_error($errorMessage . ' (Attempt ' . $attempt . '): ' . $e->getMessage(), [
                'attempt' => $attempt,
                'max_retries' => $retries,
                'exception_class' => get_class($e),
                'exception_file' => $e->getFile(),
                'exception_line' => $e->getLine(),
                'delay_ms' => $delay
            ]);
            if ($attempt == $retries) {
                throw $e;
            }
            usleep($delay * 1000);
        }
    }
}
